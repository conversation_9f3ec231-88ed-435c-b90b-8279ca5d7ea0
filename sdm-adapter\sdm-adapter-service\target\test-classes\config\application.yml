# ===================================================================
# Spring Boot configuration.
#
# This configuration is used for unit/integration tests.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

eureka:
  client:
    enabled: false
  instance:
    appname: SdmAdapterService
    instanceId: SdmAdapterService:${spring.application.instance-id:${random.value}}

spring:
  application:
    name: SdmAdapterService
  cloud:
    config:
      enabled: false
  jackson:
    serialization:
      write-durations-as-timestamps: false
  mail:
    host: localhost
  main:
    allow-bean-definition-overriding: true
  messages:
    basename: i18n/messages
  task:
    execution:
      thread-name-prefix: sdm-adapter-service-task-
      pool:
        core-size: 1
        max-size: 50
        queue-capacity: 10000
    scheduling:
      thread-name-prefix: sdm-adapter-service-scheduling-
      pool:
        size: 1
  thymeleaf:
    mode: HTML

server:
  port: 10344
  address: localhost

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  clientApp:
    name: 'sdmAdapterServiceApp'
  logging:
    # To test json console appender
    use-json-format: false
    logstash:
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 (you can type `echo 'secret-key'|base64` on your command line)
        base64-secret: YjEzMjkwZTY3ZjJlMTg0MjM4ZTY1YzY5MTc0NjQyNTMxMDUzZjk5ZTQwN2YyMTg4MWMyOWJkZGJjNjU1OTA1Nzk1YTgxMDE0YTU1YzkyNDE4Yzc5OTQyNTE4NmM4ZTJjZGNkMTgwNDI4NDBjMDhhNDdmNWNhNTA5MzRiYjgxMDY=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

application:
  clientType: FLOWONE
  conductor:
    url: http://************:31749/api/
    workerThreads: 20
    mnoPrefix:
  ratTypes: ',,,,I-HSPA-Evolution,E-UTRAN,NB-IoT,LTE-M,99,99'

onends:
  includeQosInApn: false
  trustStore: classpath:config/keys/poc_ntt.jks
  keyStore: classpath:config/keys/ntsysopt2-client.keystore
  password: provgw
  url: http://localhost:9191/ProvisioningGateway/services/SPMLNWSubscriber10Service
  hlrNsrUrl: http://localhost:9191/ProvisioningGateway/services/SPMLHlrNsr21Service
  hssUnifiedNsrUrl: http://localhost:9191/ProvisioningGateway/services/SPMLHssUnifiedNsr10Service
  http:
    maxConnections: 25
    maxConnectionsPerRoute: 15
    connectionTimeOut: 1200
  charging:
    chargingCharacterAllowed: false
    chargingCharacterProfile: 5
    chargingCharacterBehavior: 0
  csd: BS21
  eps:
    maxBandwidthUp: 1000000000
    maxBandwidthDown: 2000000000

ntt:
  includeQosInApn: false
  trustStore: classpath:config/keys/poc_ntt.jks
  keyStore: classpath:config/keys/ntsysopt2-client.keystore
  password: provgw
  url: http://localhost:9191/ProvisioningGateway/services/SPMLNWSubscriber10Service
  hlrNsrUrl: http://localhost:9191/ProvisioningGateway/services/SPMLHlrNsr21Service
  hssUnifiedNsrUrl: http://localhost:9191/ProvisioningGateway/services/SPMLHssUnifiedNsr10Service
  http:
    maxConnections: 25
    maxConnectionsPerRoute: 15
    connectionTimeOut: 1200
  charging:
    chargingCharacterAllowed: false
    chargingCharacterProfile: 5
    chargingCharacterBehavior: 0
  csd: BS21
  eps:
    maxBandwidthUp: 1000000000
    maxBandwidthDown: 2000000000

flowone:
  includeQosInApn: false
  trustStore: classpath:config/keys/flowone.jks
  keyStore: classpath:config/keys/ntsysopt2-client.keystore
  password: password
  url: http://************:31596/castlemock/mock/soap/project/f7ca5n/InstantLinkWebServicesPort
  netType: USCC
  orderNo: CRM_ORDER_NUMBER
  ReqUser: WDH_USER
  technicalProduct: CreateIOTService
  productVersion: 1.0
  ilReqGroup: CreateIOTService
  reqHeaderUsername: bss
  reqHeaderPassword: client
  http:
    maxConnections: 25
    maxConnectionsPerRoute: 15
    connectionTimeOut: 120000
  csd: BS21
  eps:
    maxBandwidthUp: 1000000000
    maxBandwidthDown: 2000000000
  tops:
    url: http://************:31596/castlemock/mock/soap/project/3LRjye/ResellerManagement_v1_0_Port
    billMgmtUrl: http://localhost:8080/enterprise/billing/billmanagement/v1_1
    paymentUrl: http://localhost:9080/enterprise/ar/payment/v2_1
    maxRetry: 4
    delay: 0
    keyStore: classpath:config/keys/tops_client.keystore
    keyStorePassword: nokiawdh
    trustStore: classpath:config/keys/tops_server.jks
    trustStorePassword: password
    authId: test
    authValue: test
    clientId: test
    uscSecurityToken: test

  kafka: wdh.sdm.adapter.audit.log.events
    #auditLogTopic: wdh

cai3g:
  location: ire
  trustStore: classpath:config/keys/windtre_eda.jks
  tspassword: windtre
  #user: wing
  #password: Wing001!
  user: WINGSDM
  password: WingSDM@Prov1
  url: https://localhost:8181/CAI3G1.2/services/CAI3G1.2
  #hlrUrl: https://localhost:8181/WING/Dispatcher
  #hssUrl: https://localhost:8181/WING/Dispatcher
  hlrUrl: https://localhost:8181/CAI3G1.2/services/CAI3G1.2
  hssUrl: https://localhost:8181/CAI3G1.2/services/CAI3G1.2
  sessionPoolSize: 5
  #hlrMoType: WINGSubscription@http://schemas.ericsson.com/ma/CA/WINGSubscription/
  #hssMoType: WINGEPSMultiSC@http://schemas.ericsson.com/ma/CA/WINGEPSMultiSC/
  hlrMoType: Subscription@http://schemas.ericsson.com/ema/UserProvisioning/GsmHlr/
  hssMoType: EPSMultiSC@http://schemas.ericsson.com/ma/HSS/
  aucMoType: Subscription@http://schemas.ericsson.com/ema/UserProvisioning/GsmAuc/
  http:
    maxConnections: 25
    maxConnectionsPerRoute: 15
    connectionTimeOut: 1200
  camel:
    tdps:
      -
        serviceName: 222886-orig-trig
        csiState: 1
        csiNotify: 0
        triggeringPoint: 0
        detectionPoint: 2
        gsmScfAddress: 393205800606
        serviceKey: 52447
        defaultErrorHandling: 1
        cch: 1
        i: true
        dialnum:
      -
        serviceName: 222886-term-trig
        csiState: 1
        csiNotify: 0
        triggeringPoint: 1
        detectionPoint: 12
        gsmScfAddress: 393205800606
        serviceKey: 53447
        defaultErrorHandling: 1
        cch: 2
        i: true
        dialnum:


{"groups": [{"name": "neo", "type": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties"}, {"name": "neo.audit", "type": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Audit", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties.Audit getAudit() "}, {"name": "neo.defaults", "type": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties.Defaults getDefaults() "}, {"name": "neo.messaging", "type": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Messaging", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties.Messaging getMessaging() "}, {"name": "neo.messaging.kafka", "type": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Messaging", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties.Kafka getKafka() "}, {"name": "<PERSON><PERSON>o<PERSON><PERSON>", "type": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$OAuth", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties.OAuth getOauth() "}, {"name": "neo.soap-security", "type": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$SoapSecurity", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties.SoapSecurity getSoapSecurity() "}, {"name": "neo.ssl", "type": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties", "sourceMethod": "public com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties.Ssl getSsl() "}], "properties": [{"name": "neo.audit.enable-message-truncation", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Audit"}, {"name": "neo.audit.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Audit"}, {"name": "neo.audit.include-error-events", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Audit"}, {"name": "neo.audit.include-full-payloads", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Audit"}, {"name": "neo.audit.include-oauth-events", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Audit"}, {"name": "neo.audit.include-ossj-operations", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Audit"}, {"name": "neo.audit.include-web-service-events", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Audit"}, {"name": "neo.audit.kafka-topic", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Audit"}, {"name": "neo.audit.max-field-length", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Audit"}, {"name": "neo.audit.max-total-message-size", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Audit"}, {"name": "neo.connection-timeout", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties"}, {"name": "neo.defaults.acwacctsubtype", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.apn-name", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.arp-preempt-cap", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.arp-preempt-vuln", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.caller-id", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.carrier-id", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.charging-characteristic", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.client-application-id", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.emlpp-active", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.equipment-type", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.external-offer-id", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.imsi-prefix", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.iwk-eps-ind", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.market", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.offer-level", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.operator-id", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.originating-class-of-service", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.ota-validity", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.pdp-name", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.sms", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.sub-market", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.defaults.subscription-class", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Defaults"}, {"name": "neo.messaging.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Messaging"}, {"name": "neo.messaging.kafka.acks", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.backoff-multiplier", "type": "java.lang.Double", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.batch-size", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.bootstrap-servers", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.buffer-memory", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.circuit-breaker-reset-timeout-ms", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.circuit-breaker-timeout-ms", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.compression-type", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.delivery-timeout-ms", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.enable-idempotence", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.enable-jitter", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.initial-retry-delay-ms", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.jitter-percent", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.key-serializer", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.linger-ms", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.max-block-ms", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.max-consecutive-failures", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.max-in-flight-requests-per-connection", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.max-request-size", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.max-retries", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.request-timeout-ms", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.retry-backoff-max-ms", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.retry-backoff-ms", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.transaction-timeout-ms", "type": "java.lang.Long", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.transactional-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.transactional-id", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.messaging.kafka.value-serializer", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Kafka"}, {"name": "neo.oauth.authentication-method", "type": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$AuthenticationMethod", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$OAuth"}, {"name": "neo.oauth.client-id", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$OAuth"}, {"name": "neo.oauth.client-secret", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$OAuth"}, {"name": "neo.oauth.grant-type", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$OAuth"}, {"name": "neo.oauth.scope", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$OAuth"}, {"name": "neo.oauth.token-endpoint", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$OAuth"}, {"name": "neo.oauth.token-refresh-buffer", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$OAuth"}, {"name": "neo.read-timeout", "type": "java.lang.Integer", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties"}, {"name": "neo.service-url", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties"}, {"name": "neo.soap-security.enable-ws-timestamp", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$SoapSecurity"}, {"name": "neo.soap-security.enable-ws-username-token", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$SoapSecurity"}, {"name": "neo.soap-security.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$SoapSecurity"}, {"name": "neo.soap-security.http-authentication-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$SoapSecurity"}, {"name": "neo.soap-security.password", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$SoapSecurity"}, {"name": "neo.soap-security.username", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$SoapSecurity"}, {"name": "neo.ssl.disable-certificate-validation", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}, {"name": "neo.ssl.disable-hostname-verification", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}, {"name": "neo.ssl.enable-ssl-debug", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}, {"name": "neo.ssl.https-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}, {"name": "neo.ssl.key-password", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}, {"name": "neo.ssl.keystore-password", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}, {"name": "neo.ssl.keystore-path", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}, {"name": "neo.ssl.keystore-type", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}, {"name": "neo.ssl.mtls-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}, {"name": "neo.ssl.truststore-password", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}, {"name": "neo.ssl.truststore-path", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}, {"name": "neo.ssl.truststore-type", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}, {"name": "neo.ssl.validate-certificates", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties$Ssl"}], "hints": []}
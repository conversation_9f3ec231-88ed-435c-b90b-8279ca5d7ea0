# ===================================================================
# Spring Boot configuration.
#
# This configuration will be overridden by the Spring profile you use,
# for example application-dev.yml if you use the "dev" profile.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

eureka:
  client:
    enabled: false
    healthcheck:
      enabled: false
    fetch-registry: false
    register-with-eureka: false
    instance-info-replication-interval-seconds: 10
    registry-fetch-interval-seconds: 10
  instance:
    appname: sdmadapterservice
    instanceId: sdmadapterservice:${spring.application.instance-id:${random.value}}
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 10
    status-page-url-path: ${management.endpoints.web.base-path}/info
    health-check-url-path: ${management.endpoints.web.base-path}/health
    metadata-map:
      zone: primary # This is needed for the load balancer
      profile: ${spring.profiles.active}
      version: 25.06.D01.14
      git-version: ${git.commit.id.describe:}
      git-commit: ${git.commit.id.abbrev:}
      git-branch: ${git.branch:}
      context-path: ${server.servlet.context-path:}
feign:
  circuitbreaker:
    enabled: true
  # client:
  #   config:
  #     default:
  #       connectTimeout: 5000
  #       readTimeout: 5000
management:
  endpoints:
    web:
      base-path: /management
      exposure:
        include: ['configprops', 'env', 'health', 'info', 'jhimetrics', 'logfile', 'loggers', 'prometheus', 'threaddump']
  endpoint:
    health:
      show-details: WHEN_AUTHORIZED
      roles: 'ROLE_ADMIN'
      probes:
        enabled: true
    jhimetrics:
      enabled: true
  info:
    git:
      mode: full
  health:
    group:
      liveness:
        include: livenessState
      readiness:
        include: readinessState
    mail:
      enabled: false # When using the MailService, configure an SMTP server and set this to true
  metrics:
    export:
      # Prometheus is the default metrics backend
      prometheus:
        enabled: true
        step: 60
    enable:
      http: true
      jvm: true
      logback: true
      process: true
      system: true
    distribution:
      percentiles-histogram:
        all: true
      percentiles:
        all: 0, 0.5, 0.75, 0.95, 0.99, 1.0
    tags:
      application: ${spring.application.name}
    web:
      server:
        request:
          autotime:
            enabled: true
springfox:
  documentation:
    auto-startup: false
spring:
 #Use this to befriend spring-boot-starter-actuator & springfox-boot-starter
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  application:
    name: SdmAdapterService
  profiles:
    # The commented value for `active` can be replaced with valid Spring profiles to load.
    # Otherwise, it will be filled in by maven when building the JAR file
    # Either way, it can be overridden by `--spring.profiles.active` value passed in the commandline or `-Dspring.profiles.active` set in `JAVA_OPTS`
    active: #spring.profiles.active#
    group:
      dev:
        - dev
        - api-docs
        # Uncomment to activate TLS for the dev profile
        #- tls
  jmx:
    enabled: false
  messages:
    basename: i18n/messages
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    compatibility-verifier.enabled: false
  task:
    execution:
      thread-name-prefix: sdm-adapter-service-task-
      pool:
        core-size: 2
        max-size: 50
        queue-capacity: 10000
    scheduling:
      thread-name-prefix: sdm-adapter-service-scheduling-
      pool:
        size: 2
  thymeleaf:
    mode: HTML
  output:
    ansi:
      console-available: true
  kafka:
    bootstrap-servers: ************:9092

server:
  servlet:
    session:
      cookie:
        http-only: true

# Properties to be exposed on the /info management endpoint
info:
  # Comma separated list of profiles that will trigger the ribbon to show
  display-ribbon-on-profiles: 'dev'

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  clientApp:
    name: 'sdmAdapterServiceApp'
  # By default CORS is disabled. Uncomment to enable.
  # cors:
  #   allowed-origins: "http://localhost:8100,http://localhost:9000"
  #   allowed-methods: "*"
  #   allowed-headers: "*"
  #   exposed-headers: "Authorization,Link,X-Total-Count,X-${jhipster.clientApp.name}-alert,X-${jhipster.clientApp.name}-error,X-${jhipster.clientApp.name}-params"
  #   allow-credentials: true
  #   max-age: 1800
  mail:
    from: SdmAdapterService@localhost
  api-docs:
    default-include-pattern: ${server.servlet.context-path:}/api/.*
    management-include-pattern: ${server.servlet.context-path:}/management/.*
    title: Sdm Adapter (${application.clientType}) Service API
    description: Sdm Adapter (${application.clientType}) Service API documentation
    version: 25.06.D01.14
    terms-of-service-url:
    contact-name:
    contact-url:
    contact-email:
    license: unlicensed
    license-url:
  security:
    content-security-policy: "default-src 'self'; frame-src 'self' data:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://storage.googleapis.com; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:"
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

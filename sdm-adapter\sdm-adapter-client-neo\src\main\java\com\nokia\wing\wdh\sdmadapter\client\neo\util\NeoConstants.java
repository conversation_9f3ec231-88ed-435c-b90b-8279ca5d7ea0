package com.nokia.wing.wdh.sdmadapter.client.neo.util;

/**
 * Consolidated constants for NEO OSSJ adapter client.
 *
 * This class contains all constants used throughout the NEO client implementation,
 * including service operation constants, characteristic names, default values,
 * and mapping constants. Consolidated from RequestConstants, NeoConstants, and
 * NeoMappingConstants for better organization and maintainability.
 */
public final class NeoConstants {

    // ========================================
    // SERVICE OPERATION CONSTANTS
    // ========================================

    /** Service type for mobility operations */
    public static final String SERVICE_TYPE = "IOT-CMP";

    /** Action constants for OSSJ service operations */
    public static final String ACTION_PROVIDE = "Provide";
    public static final String ACTION_CEASE = "cease";
    public static final String ACTION_SUSPEND = "suspend";
    public static final String ACTION_RESUME = "resume";

    /** Operator identifier for BSS operations */
    public static final String OPERATOR_ACM = "ACM";

    /** Service order type identifier */
    public static final String SERVICE_ORDER_TYPE = "Service";      // For service keys
    public static final String SERVICE_ORDER_KEY_TYPE = "Service";  // For service order keys

    /** Priority levels for different operations */
    public static final String PRIORITY = "priority";
    public static final int PRIORITY_PROVIDE = 2;
    public static final int PRIORITY_CEASE = 6;
    public static final int PRIORITY_SUSPEND = 0;
    public static final int PRIORITY_RESUME = 3;

    /** Activation target identifier */
    public static final String ACTIVATION_TARGET_ID = "Activation";

    /** Empty sub-action constant */
    public static final String SUB_ACTION_EMPTY = "";

    // ========================================
    // SUBSCRIBER IDENTITY CONSTANTS
    // ========================================

    /** Mobile Station International Subscriber Directory Number */
    public static final String MSISDN = "msisdn";
    public static final String PRIMARY_PUID = "PrimaryPUID";
    public static final String BASIC_MSISDN_BC_NAME = "basicMsisdnBcName";
    public static final String BASIC_MSISDN_BC_NAME_NORTH_RIM = "NULL";
    public static final String BASIC_MSISDN_BC_NAME_DEFAULT = "SPEECH & GPRS";

    /** International Mobile Subscriber Identity */
    public static final String IMSI = "imsi";

    /** Subscription identifier */
    public static final String SUBSCRIPTION_ID = "subscriptionId";

    /** Caller identification */
    public static final String CALLER_ID = "callerId";

    /** Integrated Circuit Card Identifier */
    public static final String ICCID = "iccid";

    // ========================================
    // PAYMENT AND BILLING CONSTANTS
    // ========================================

    /** Payment and billing characteristics */
    public static final String PAYMENT_TYPE = "paymentType";
    public static final String PAYMENT_TYPE_DEFAULT = "PO";
    public static final String BILLING_ACCOUNT_NUMBER = "billingAccountNumber";

    // ========================================
    // SMS SERVICE CONSTANTS
    // ========================================

    /** SMS service characteristics */
    public static final String SMS = "sms";
    public static final String IS_BAR_OUTGOING_SMS = "isBarOutgoingSms";
    public static final String IS_BAR_INCOMING_SMS = "isBarIncomingSms";
    public static final String IS_BAR_OUTGOING_INTERNATIONAL_SMS = "isBarOutgoingInternationalSms";
    public static final String SHORT_MESSAGE_TERMINATION_AUTHORIZATION = "shortMessageTerminationAuthorization";
    public static final String SHORT_MESSAGE_TERMINATION_AUTHORIZATION_DEFAULT = "0";

    // ========================================
    // VOICE SERVICE CONSTANTS
    // ========================================

    /** Voice service characteristics */
    public static final String IS_BAR_INCOMING_CALLS = "isBarIncomingCalls";
    public static final String IS_BAR_OUTGOING_CALLS = "isBarOutgoingCalls";

    /** CLIP (Calling Line Identification Presentation) */
    public static final String CLIP_ACT = "clipAct";
    public static final String CLIP_ACT_DEFAULT = "TRUE";
    public static final String CLIP_AUTH = "clipAuth";
    public static final String CLIP_AUTH_DEFAULT = "TRUE";

    /** CLIR (Calling Line Identification Restriction) */
    public static final String CLIR_ACT = "clirAct";
    public static final String CLIR_ACT_DEFAULT = "TRUE";
    public static final String CLIR_AUTH = "clirAuth";
    public static final String CLIR_AUTH_DEFAULT = "TRUE";

    /** Call Waiting (CW) */
    public static final String CW_AUTH = "cwAuth";
    public static final String CW_AUTH_DEFAULT = "FALSE";
    public static final String CW_ACT = "cwAct";
    public static final String CW_ACT_DEFAULT = "FALSE";

    /** Multi-Party (MPTY) */
    public static final String MPTY_AUTH = "mptyAuth";
    public static final String MPTY_AUTH_DEFAULT = "FALSE";
    public static final String MPTY_ACT = "mptyAct";
    public static final String MPTY_ACT_DEFAULT = "FALSE";

    /** Call Hold */
    public static final String HOLD_AUTH = "holdAuth";
    public static final String HOLD_AUTH_DEFAULT = "0";
    public static final String HOLD_ACT = "holdAct";
    public static final String HOLD_ACT_DEFAULT = "0";

    /** Call Forwarding - Busy (CFB) */
    public static final String CFB_AUTH = "cfbAuth";
    public static final String CFB_AUTH_DEFAULT = "FALSE";
    public static final String TELE_CFB_NOTIF_CALL_PARTY = "teleCfbNotifCallParty";
    public static final String TELE_CFB_NOTIF_CALL_PARTY_DEFAULT = "FALSE";

    /** Call Forwarding - No Reply (CFNRC) */
    public static final String CFNRC_AUTH = "cfnrcAuth";
    public static final String CFNRC_AUTH_DEFAULT = "FALSE";
    public static final String TELE_CFNRC_NOTIF_CALL_PARTY = "teleCfnrcNotifCallParty";
    public static final String TELE_CFNRC_NOTIF_CALL_PARTY_DEFAULT = "FALSE";

    /** Call Forwarding - No Reply (CFNRY) */
    public static final String CFNRY_AUTH = "cfnryAuth";
    public static final String CFNRY_AUTH_DEFAULT = "FALSE";
    public static final String TELE_CFNRY_NOTIF_CALL_PARTY = "teleCfnryNotifCallParty";
    public static final String TELE_CFNRY_NOTIF_CALL_PARTY_DEFAULT = "FALSE";

    /** Call Forwarding - Deflection (CFD) */
    public static final String CFD_AUTH = "cfdAuth";
    public static final String CFD_AUTH_DEFAULT = "FALSE";
    public static final String TELE_CFD_ACT = "teleCfdAct";
    public static final String TELE_CFD_ACT_DEFAULT = "FALSE";
    public static final String TELE_CFD_FTN_ADDR = "teleCfdFtnAddr";
    public static final String TELE_CFD_FTN_ADDR_DEFAULT = "1/1/1/0";
    public static final String TELE_CFD_FTN_SUB_ADDR = "teleCfdFtnSubAddr";
    public static final String TELE_CFD_FTN_SUB_ADDR_DEFAULT = "1/0/";

    /** USSD (Unstructured Supplementary Service Data) */
    public static final String SUBS_USSD_ENABLED = "subsUssdEnabled";
    public static final String SUBS_USSD_ENABLED_DEFAULT = "1";

    /** Password and Security */
    public static final String PASSWORD_OPTION_FOR_SDB = "passwordOptionForSdb";
    public static final String PASSWORD_OPTION_FOR_SDB_DEFAULT = "TRUE";

    /** GSM Subscription Restrictions */
    public static final String GSM_SUBSCRIPT_RESTRICT = "gsmSubscriptRestrict";
    public static final String GSM_SUBSCRIPT_RESTRICT_DEFAULT = "FALSE";



    // TELE-CFU-ACT
    public static final String CFU_AUTH = "cfuAuth";
    public static final String CFU_AUTH_DEFAULT = "FALSE";
    public static final String TELE_CFU_ACT = "teleCfuAct";
    public static final String TELE_CFU_ACT_DEFAULT = "FALSE";

    // INTERZONAL-INTL-OG-NOT-HPLMN
    public static final String INTERZONAL_INTL_OG_NOT_HPLMN = "interzonalIntlOgNotHplmn";
    public static final String INTERZONAL_INTL_OG_NOT_HPLMN_DEFAULT = "FALSE";

    // INTL-OG-CALLS-NOT-HPLMN
    public static final String INTL_OG_CALLS_NOT_HPLMN = "intlOgCallsNotHplmn";
    public static final String INTL_OG_CALLS_NOT_HPLMN_DEFAULT = "FALSE";
    public static final String IS_INTERNATIONAL = "isInternational";
    public static final String IS_INTERNATIONAL_DEFAULT = "FALSE";




    // ========================================
    // DATA SERVICE CONSTANTS
    // ========================================

    /** Data service characteristics */
    public static final String IS_BAR_WAP = "isBarWap";
    public static final String IS_BAR_WAP_DEFAULT = "FALSE";

    /** Data service quality and characteristics */
    public static final String CHARGING_CHARACTERISTIC = "chargingCharacteristic";
    public static final String ARP_PREEMPT_CAP = "arpPreemptCap";
    public static final String ARP_PREEMPT_VULN = "arpPreemptVuln";
    public static final String IWK_EPS_IND = "iwkEpsInd";

    /** Speed, AMBR, and Session Parameters (IMSI Prefix Dependent) */
    public static final String SESSION_AMBR_DOWNLINK = "sessionAmbrDownlink";
    public static final String SESSION_AMBR_UPLINK = "sessionAmbrUplink";
    public static final String DOWNLOAD_SPEED = "downloadSpeed";
    public static final String DOWNLOAD_SPEED_DEFAULT = "10 Gbps";
    public static final String UPLOAD_SPEED = "uploadSpeed";
    public static final String UPLOAD_SPEED_DEFAULT = "10 Gbps";
    public static final String ALLOWED_SESSION_TYPES = "allowedSessionTypes";
    public static final String DEFAULT_SESSION_TYPES = "defaultSessionTypes";
    public static final String QOS_PROFILE_5G = "5gQosProfile";
    public static final String ARP_PRIORITY_LEVEL = "arpPriorityLevel";
    public static final String PRIORITY_LEVEL = "priorityLevel";
    public static final String DNN_NAME = "dnnName";
    public static final String DEFAULT_DNN_NAME = "defaultDnnName";

    /** SSC Mode */
    public static final String DEFAULT_SSC_MODE = "defaultSscMode";
    public static final String DEFAULT_SSC_MODE_DEFAULT = "SSC-MODE-1";

    /** Network Slicing */
    public static final String SLICE_SERVICE_TYPE = "sliceServiceType";
    public static final String SLICE_SERVICE_TYPE_DEFAULT = "1";
    public static final String SLICE_DIFFERENTIATOR = "sliceDifferentiator";
    public static final String SLICE_DIFFERENTIATOR_DEFAULT = "NULL";

    // ========================================
    // NETWORK AND ACCESS CONSTANTS
    // ========================================

    /** Network service characteristics */
    public static final String ACWSVCNETWORK = "acwsvcnetwork";
    public static final String ACWACCTSUBTYPE = "acwacctsubtype";
    public static final String NETWORK_ACCESS_MODE = "networkAccessMode";

    /** Network access mode values */
    public static final String NETWORK_ACCESS_MODE_VOICE_SMS_DATA = "00"; // Voice + SMS + Data
    public static final String NETWORK_ACCESS_MODE_DATA_ONLY = "00";      // Data only or Data + (Voice OR SMS)
    public static final String NETWORK_ACCESS_MODE_VOICE_ONLY = "01";     // Voice only

    // ========================================
    // PDP AND DNN CONSTANTS
    // ========================================

    /** PDP and DNN characteristics */
    public static final String PDP_NAME = "pdpName";
    public static final String DEFAULT_PDP_NAME = "defaultPDPName";
    // ========================================
    // MULTIMEDIA AND CONTENT CONSTANTS
    // ========================================

    /** Multimedia service characteristics */
    public static final String MMS = "mms";
    public static final String MMS_DEFAULT = "TRUE";
    public static final String HOME_SMSC = "homeSMSC";
    public static final String VIDEO_STREAMING_QUALITY = "videoStreamingQuality";
    public static final String IP_MESSAGING_INDICATOR = "ipMessagingIndicator";
    public static final String CONTENT_FILTER = "contentFilter";
    public static final String CONTENT_FILTER_DEFAULT = "FALSE";
    public static final String IS_AGE_VERIFIED = "isAgeVerified";
    public static final String IS_AGE_VERIFIED_DEFAULT = "FALSE";

    // ========================================
    // 5G AND ADVANCED NETWORK CONSTANTS
    // ========================================

    /** 5G and advanced network characteristics */
    public static final String ACWSVCNETWORK_5GSA_PHONE = "5gsaphone";
    public static final String ACWSVCNETWORK_5G_PHONE = "5gphone";
    public static final String ACWSVCNETWORK_5G_BASIC = "5gbasic";
    public static final String ACWSVCNETWORK_NULL = "NULL";
    public static final String NR_AS_SECONDARY_RAT_ALLOWED = "nrAsSecondaryRatAllowed";
    public static final String NR_AS_SECONDARY_RAT_ALLOWED_DEFAULT = "TRUE";

    // ========================================
    // GEOGRAPHIC AND MARKET CONSTANTS
    // ========================================

    /** Geographic and market identifiers */
    public static final String MARKET = "market";
    public static final String SUBMARKET = "subMarket";
    public static final String OPERATOR_ID = "operatorId";

    // ========================================
    // CLASS OF SERVICE CONSTANTS
    // ========================================

    /** Class of service characteristics */
    public static final String ORIGINATING_CLASS_OF_SERVICE = "originatingClassOfService";
    public static final String TERMINATING_CLASS_OF_SERVICE = "terminatingClassOfService";

    // ========================================
    // ROAMING CONSTANTS
    // ========================================

    /** Roaming restriction characteristics */
    public static final String ROAMING_RESTRICTION = "roamingRestriction";
    public static final String GPRS_ROAMING_RESTRICTION = "gprsRoamingRestriction";

    // ========================================
    // SERVICE FEATURES CONSTANTS
    // ========================================

    /** Enhanced Multi-Level Precedence and Preemption */
    public static final String EMLPP_ACTIVE = "emlppActive";
    public static final String SUBSCRIPTION_CLASS = "subscriptionClass";

    // ========================================
    // APPLICATION AND CLIENT CONSTANTS
    // ========================================

    /** Application and client identifiers */
    public static final String CLIENT_APPLICATION_ID = "clientapplicationid";
    public static final String CARRIER_ID = "carrierId";
    public static final String HSS_POOL_ID = "hssPoolId";
    public static final String HSS_POOL_ID_UNDERSCORE = "hss_pool_id";
    public static final String HSS_GROUP_ID = "hssgroupid";
    public static final String HSS_GROUP_ID_UNDERSCORE = "hss_group_id";
    public static final String HSS_ID = "hssid";
    public static final String HSS_ID_UNDERSCORE = "hss_id";
    public static final String CHF_GROUP_ID = "chfgroupid";
    public static final String CHF_GROUP_ID_UNDERSCORE = "chf_group_id";
    public static final String HSA = "HSA";
    public static final String HSS_DEFAULT = "EOD";
    public static final String CORE_IND = "coreInd";
    public static final String CORE_IND_DEFAULT = "NeoCore";
    public static final String CORE_IND_IMSI_SPECIFIC = "NorthRIM";

    // ========================================
    // For NeoCore Parameters, usecase04
    // ======================================

    public static final String OTA_VALIDITY = "otaValidity";


    // ========================================
    // STATE AND STATUS CONSTANTS
    // ========================================

    /** State and status characteristics */
    public static final String CURRENT_STATE = "currentstate";
    public static final String CURRENT_STATE_UNDERSCORE = "current_state";

    // ========================================
    // EQUIPMENT AND OFFER CONSTANTS
    // ========================================

    /** Equipment and offer characteristics */
    public static final String EQUIPMENT_TYPE = "equipmentType";
    public static final String EXTERNAL_OFFER_ID = "externalOfferId";
    public static final String OFFER_LEVEL = "offerLevel";

    // ========================================
    // DATE AND TIME CONSTANTS
    // ========================================

    /** Date and time characteristics */
    public static final String EFFECTIVE_DATE = "effectiveDate";

    // ========================================
    // BOOLEAN REPRESENTATION CONSTANTS
    // ========================================

    /** Standard boolean representations - use these consistently */
    public static final String BOOLEAN_TRUE = "TRUE";
    public static final String BOOLEAN_FALSE = "FALSE";

    // ========================================
    // BOOLEAN UTILITY METHODS
    // ========================================

    /**
     * Converts a Boolean value to NEO-compatible string representation.
     *
     * @param value The Boolean value to convert (can be null)
     * @return "TRUE" if value is true, "FALSE" if value is false, null if value is null
     */
    public static String toNeoBoolean(Boolean value) {
        if (value == null) {
            return null;
        }
        return value ? BOOLEAN_TRUE : BOOLEAN_FALSE;
    }

    /**
     * Converts a Boolean value to NEO-compatible string representation with default.
     *
     * @param value The Boolean value to convert (can be null)
     * @param defaultValue The default string to return if value is null
     * @return "TRUE" if value is true, "FALSE" if value is false, defaultValue if value is null
     */
    public static String toNeoBoolean(Boolean value, String defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        return value ? BOOLEAN_TRUE : BOOLEAN_FALSE;
    }

    // Private constructor to prevent instantiation
    private NeoConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}

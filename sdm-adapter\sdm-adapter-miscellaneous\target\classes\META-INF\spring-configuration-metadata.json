{"groups": [{"name": "kafka", "type": "com.nokia.wing.wdh.miscellaneous.config.KafkaMiscellaneousProperties", "sourceType": "com.nokia.wing.wdh.miscellaneous.config.KafkaMiscellaneousProperties"}], "properties": [{"name": "kafka.boot-strap-servers", "type": "java.lang.String", "sourceType": "com.nokia.wing.wdh.miscellaneous.config.KafkaMiscellaneousProperties"}, {"name": "kafka.consumer-props", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "com.nokia.wing.wdh.miscellaneous.config.KafkaMiscellaneousProperties"}, {"name": "kafka.producer-props", "type": "java.util.Map<java.lang.String,java.lang.Object>", "sourceType": "com.nokia.wing.wdh.miscellaneous.config.KafkaMiscellaneousProperties"}], "hints": []}
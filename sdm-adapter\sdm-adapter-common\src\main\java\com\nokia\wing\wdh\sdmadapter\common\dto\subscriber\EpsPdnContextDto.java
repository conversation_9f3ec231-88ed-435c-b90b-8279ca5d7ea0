package com.nokia.wing.wdh.sdmadapter.common.dto.subscriber;

import org.springframework.http.HttpHeaders;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EpsPdnContextDto {

	private Long id;
	private PdnTypeDto type;
	private String qosProfile;
	private String apn;
	private Boolean pdnGwDynamicAllocation ;
	private Boolean vplmnAddressAllowed ;
	private Long maxBandwidthUpload ;
	private Long maxBandwidthDownload ;

    //below 2 param for 5G NSA
    private Long extMaxBandwidthUp;
    private Long extMaxBandwidthDown;

    //5g parameter for throttling
    private String ueAmbrUpLink;
    private String ueAmbrDownLink;

    private String ipv4Address;
	private String ipv6Address;
    private Long qosProfileId;

    private Long apnId;
    private Long pdpId;
	private HttpHeaders header;
    //new parameters for neo
    private String pdpName;
    private String earp;
    private String five_qi_qci;
    private String sessionAmbrUpLink;
    private String sessionAmbrDownLink;
}

# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    com.nokia.wing.wdh: INFO
    com.netflix.conductor: INFO
    '[com.netflix.conductor.client]': INFO
    sun.net.www.protocol.http: INFO
    sun.net.www: INFO
    ROOT: INFO
    org.springframework.ws: INFO
    #org.apache.http: DEBUG
    # NEO Adapter specific logging
    com.nokia.wing.wdh.sdmadapter: INFO
    com.nokia.wing.wdh.sdmadapter.client.neo: DEBUG
    com.nokia.wing.wdh.sdmadapter.client.neo.security.http: DEBUG
#     com.nokia.wing.wdh.sdmadapter.client.neo.messaging: DEBUG
#     com.nokia.wing.wdh.sdmadapter.client.neo.auditlogs: DEBUG
#     com.nokia.wing.wdh.sdmadapter.client.neo.response: DEBUG
    org.springframework.security.oauth2: INFO
    org.springframework.ws.client.core: INFO
    org.springframework.ws.transport.http: INFO
    org.springframework.kafka: INFO
    # Additional debugging for NEO integration
    org.springframework.boot.autoconfigure.condition: INFO
    org.springframework.context.support: INFO
    org.springframework.beans.factory.support: INFO
apm:
  server:
    url:

eureka:
  instance:
    prefer-ip-address: true
  client:
    service-url:
      defaultZone: http://admin:${jhipster.registry.password}@************:32476/eureka/

spring:
  application:
    name: sdm-adapter
  devtools:
    restart:
      enabled: true
      additional-exclude: static/**
    livereload:
      enabled: false # we use Webpack dev server + BrowserSync for livereload
  jackson:
    serialization:
      indent-output: true
  cloud:
    config:
      uri: http://admin:${jhipster.registry.password}@************:32476/config
      # name of the config server's property source (file.yml) that we want to use
      name: SdmAdapterService
      profile: dev,api-docs
      label: main # toggle to switch to a different version of the configuration as stored in git
      # it can be set to any label, branch or commit of the configuration source Git repository
  messages:
    cache-duration: PT1S # 1 second, see the ISO 8601 standard
  thymeleaf:
    cache: false
  sleuth:
    sampler:
      probability: 1 # report 100% of traces
  zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
    base-url: http://localhost:9411
    enabled: false
    locator:
      discovery:
        enabled: true


server:
  port: 9191

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  registry:
    password: admin
  # CORS is disabled by default on microservices, as you should access them through a gateway.
  # If you want to enable it, please uncomment the configuration below.
  # cors:
  #   allowed-origins: "http://localhost:9000,https://localhost:9000"
  #   allowed-methods: "*"
  #   allowed-headers: "*"
  #   exposed-headers: "Authorization,Link,X-Total-Count"
  #   allow-credentials: true
  #   max-age: 1800
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
        base64-secret: YjEzMjkwZTY3ZjJlMTg0MjM4ZTY1YzY5MTc0NjQyNTMxMDUzZjk5ZTQwN2YyMTg4MWMyOWJkZGJjNjU1OTA1Nzk1YTgxMDE0YTU1YzkyNDE4Yzc5OTQyNTE4NmM4ZTJjZGNkMTgwNDI4NDBjMDhhNDdmNWNhNTA5MzRiYjgxMDY=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

application:
  clientType: NEO
  conductor:
    url: http://************:8080/api/
    workerThreads: 20
    workers: 4
    mnoPrefix:
  ratTypes: ',,,,I-HSPA-Evolution,E-UTRAN,NB-IoT,LTE-M,99,99'

onends:
  includeQosInApn: false
  trustStore: classpath:config/keys/poc_ntt.jks
  keyStore: classpath:config/keys/ntsysopt2-client.keystore
  password: provgw
  url: http://localhost:9191/ProvisioningGateway/services/SPMLNWSubscriber10Service
  hlrNsrUrl: http://localhost:9191/ProvisioningGateway/services/SPMLHlrNsr21Service
  hssUnifiedNsrUrl: http://localhost:9191/ProvisioningGateway/services/SPMLHssUnifiedNsr10Service
  http:
    maxConnections: 25
    maxConnectionsPerRoute: 15
    connectionTimeOut: 1200
    socketTimeout: 30000
  charging:
    chargingCharacterAllowed: false
    chargingCharacterProfile: 5
    chargingCharacterBehavior: 0
  csd: BS21
  eps:
    maxBandwidthUp: 1000000000
    maxBandwidthDown: 2000000000
  subscriberITFVersion: NW_SUBSCRIBER_v10
  allowedNetwork: NON_5G #ONLY_5G, ALL_WITH_5G

ntt:
  includeQosInApn: false
  trustStore: classpath:config/keys/poc_ntt.jks
  keyStore: classpath:config/keys/ntsysopt2-client.keystore
  password: provgw
  url: http://localhost:9191/ProvisioningGateway/services/SPMLNWSubscriber10Service
  hlrNsrUrl: http://localhost:9191/ProvisioningGateway/services/SPMLHlrNsr21Service
  hssUnifiedNsrUrl: http://localhost:9191/ProvisioningGateway/services/SPMLHssUnifiedNsr10Service
  http:
    maxConnections: 25
    maxConnectionsPerRoute: 15
    connectionTimeOut: 1200
  charging:
    chargingCharacterAllowed: false
    chargingCharacterProfile: 5
    chargingCharacterBehavior: 0
  csd: BS21
  eps:
    maxBandwidthUp: 1000000000
    maxBandwidthDown: 2000000000

flowone:
  includeQosInApn: false
  trustStore: classpath:config/keys/flowone.jks
  keyStore: classpath:config/keys/ntsysopt2-client.keystore
  password: password
  url: http://************:31596/castlemock/mock/soap/project/f7ca5n/InstantLinkWebServicesPort
  netType: USCC
  orderNo: CRM_ORDER_NUMBER
  ReqUser: WDH_USER
  technicalProduct: CreateIOTService
  productVersion: 1.0
  ilReqGroup: CreateIOTService
  reqHeaderUsername: bss
  reqHeaderPassword: client
  http:
    maxConnections: 25
    maxConnectionsPerRoute: 15
    connectionTimeOut: 120000
  csd: BS21
  eps:
    maxBandwidthUp: 1000000000
    maxBandwidthDown: 2000000000
  tops:
    url: http://************:31596/castlemock/mock/soap/project/3LRjye/ResellerManagement_v1_0_Port
    billMgmtUrl: http://************:31596/castlemock/mock/soap/project/w75HwG/BillManagement_v1_0_Port
    paymentUrl: http://************:31596/castlemock/mock/soap/project/XXJV1V/Payment_v2_0_Port
    maxRetry: 4
    delay: 0
    keyStore: classpath:config/keys/tops_client.keystore
    keyStorePassword: nokiawdh
    trustStore: classpath:config/keys/tops_server.jks
    trustStorePassword: password
    authId: test
    authValue: test
    clientId: test
    uscSecurityToken: test
    soapAction:
      billPdfAction: http://billing.uscellular.com/wsdl/enterprise/billing/billmanagement/v1_0#BillManagement_v1_0_Binding.getBillPdf
      billListAction: http://billing.uscellular.com/wsdl/enterprise/billing/billmanagement/v1_0#BillManagement_v1_0_Binding.getBillList
      trxHistoryAction: http://services.uscellular.com/wsdl/enterprise/ar/payment/v2_1#Payment_v2_0_Binding.getPaymentTransactionHistory

  kafka: wdh.sdm.adapter.audit.log.events
    #auditLogTopic: wdh

neo:
  serviceUrl: ${NEO_SERVICE_URL:http://localhost:8082/neowsdl/services/AUAI-RequestWS-v2-0}
  connectionTimeout: 45000
  readTimeout: 120000
  oauth:
    authenticationMethod: ${NEO_AUTH_METHOD:azure-sdk}
    # - azure-sdk: Uses Azure SDK DefaultAzureCredential (supports multiple auth sources)
    scope: "api://BSSE-NPRD/.default"
    tokenRefreshBuffer: 900 #Refresh 15 mins before expiry
  ssl:
    mtlsEnabled: ${NEO_MTLS_ENABLED:false}
    httpsEnabled: ${NEO_HTTPS_ENABLED:false}
    truststorePath: ${NEO_TRUSTSTORE_PATH:classpath:certificates/client-truststore.jks}
    truststorePassword: ${NEO_TRUSTSTORE_PASSWORD:changeit}
    truststoreType: ${NEO_TRUSTSTORE_TYPE:JKS}
    keystorePath: ${NEO_KEYSTORE_PATH:classpath:certificates/client1.jks}
    keystorePassword: ${NEO_KEYSTORE_PASSWORD:changeit}
    keyPassword: ${NEO_KEY_PASSWORD:changeit}
    keystoreType: ${NEO_KEYSTORE_TYPE:JKS}
    disableHostnameVerification: ${NEO_DISABLE_HOSTNAME_VERIFICATION:true}
    enableSslDebug: ${NEO_SSL_DEBUG_ENABLED:false}
    validateCertificates: ${NEO_VALIDATE_CERTIFICATES:true}
    disableCertificateValidation: ${NEO_DISABLE_CERTIFICATE_VALIDATION:false}
  soapSecurity:
    enabled: ${NEO_SOAP_SECURITY_ENABLED:false}
    httpAuthenticationEnabled: ${NEO_HTTP_AUTH_ENABLED:true}
    enableWsUsernameToken: true
    username: ${NEO_SOAP_USERNAME:OUA_SOAP_USER}
    password: ${NEO_SOAP_PASSWORD:deprecated-oauth-token-used-instead}
    enableWsTimestamp: false
  audit:
    enabled: ${NEO_AUDIT_ENABLED:false}
    kafkaTopic: wdh.sdm.adapter.neo.audit.log.events
    includeWebServiceEvents: true
    includeErrorEvents: true
    includeOssjOperations: true
    includeOauthEvents: true
    maxFieldLength: ${NEO_AUDIT_MAX_FIELD_LENGTH:5000}
    maxTotalMessageSize: ${NEO_AUDIT_MAX_MESSAGE_SIZE:50000000}
    enableMessageTruncation: ${NEO_AUDIT_ENABLE_TRUNCATION:true}
    includeFullPayloads: ${NEO_AUDIT_INCLUDE_FULL_PAYLOADS:false}
  messaging:
    enabled: ${NEO_MESSAGING_ENABLED:false}  # Feature flag - disabled by default
    kafka:
      bootstrapServers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}  # Docker Kafka external port
      maxRetries: 3
      initialRetryDelayMs: 1000
      backoffMultiplier: 2.0
      keySerializer: org.apache.kafka.common.serialization.StringSerializer
      valueSerializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all  # Wait for all replicas (matches Docker setup)
      batchSize: 16384
      lingerMs: 5  # Increased for better batching with Docker
      bufferMemory: 67108864  # 64MB - increased for large audit messages
      # Message size configuration for large audit messages
      maxRequestSize: 10485760  # 10MB max request size
      # Circuit breaker configuration for graceful Kafka unavailability handling
      circuitBreakerTimeoutMs: ${NEO_KAFKA_CIRCUIT_BREAKER_TIMEOUT_MS:60000}  # 1 minute
      maxConsecutiveFailures: ${NEO_KAFKA_MAX_CONSECUTIVE_FAILURES:5}  # Open circuit after 5 failures
      circuitBreakerResetTimeoutMs: ${NEO_KAFKA_CIRCUIT_BREAKER_RESET_TIMEOUT_MS:300000}  # 5 minutes
      # Idempotent producer configuration for exactly-once semantics
      enableIdempotence: ${NEO_KAFKA_ENABLE_IDEMPOTENCE:true}
      maxInFlightRequestsPerConnection: ${NEO_KAFKA_MAX_IN_FLIGHT:5}
      transactionalEnabled: ${NEO_KAFKA_TRANSACTIONAL_ENABLED:false}
      transactionalId: ${NEO_KAFKA_TRANSACTIONAL_ID:neo-audit-producer}
      transactionTimeoutMs: ${NEO_KAFKA_TRANSACTION_TIMEOUT:60000}
      # Smart retry configuration with jitter
      retryBackoffMs: ${NEO_KAFKA_RETRY_BACKOFF:1000}
      retryBackoffMaxMs: ${NEO_KAFKA_RETRY_BACKOFF_MAX:32000}
      enableJitter: ${NEO_KAFKA_ENABLE_JITTER:true}
      jitterPercent: ${NEO_KAFKA_JITTER_PERCENT:20}
      # Compression and performance optimization, if problem then we can manipulate this
      compressionType: ${NEO_KAFKA_COMPRESSION_TYPE:none}  #snappy
      requestTimeoutMs: 30000
      deliveryTimeoutMs: 120000
      maxBlockMs: 60000
  defaults:
    imsi-prefix: ${NEO_DEFAULT_IMSI_PREFIX:3101705}  # IMSI prefix for ACWSVCNETWORK calculation
    market: ${NEO_DEFAULT_MARKET:ACM}
    sub-market: ${NEO_DEFAULT_SUB_MARKET:ACM}
    sms: ${NEO_DEFAULT_SMS:ALP}
    operator-id: ${NEO_DEFAULT_OPERATOR_ID:21}
    apn-name: ${NEO_DEFAULT_APN_NAME:d50003.etr}
    pdp-name: ${NEO_DEFAULT_PDP_NAME:D50003}
    originating-class-of-service: ${NEO_DEFAULT_ORIGINATING_CLASS_OF_SERVICE:65536}  # Default for Oct
    caller-id: ${NEO_DEFAULT_CALLER_ID:2}
    emlpp-active: ${NEO_DEFAULT_EMLPP_ACTIVE:FALSE}
    subscription-class: ${NEO_DEFAULT_SUBSCRIPTION_CLASS:WA}
    client-application-id: ${NEO_DEFAULT_CLIENT_APPLICATION_ID:ACM}
    carrier-id: ${NEO_DEFAULT_CARRIER_ID:1}
    equipment-type: ${NEO_DEFAULT_EQUIPMENT_TYPE:G}
    external-offer-id: ${NEO_DEFAULT_EXTERNAL_OFFER_ID:990029}
    offer-level: ${NEO_DEFAULT_OFFER_LEVEL:S}
    charging-characteristic: ${NEO_DEFAULT_CHARGING_CHARACTERISTIC:0000}
    arp-preempt-cap: ${NEO_DEFAULT_ARP_PREEMPT_CAP:NOT_PREEMPT}
    arp-preempt-vuln: ${NEO_DEFAULT_ARP_PREEMPT_VULN:PREEMPTABLE}
    iwk-eps-ind: ${NEO_DEFAULT_IWK_EPS_IND:FALSE}
    ota-validity: ${NEO_DEFAULT_OTA_VALIDITY:691200}
    acwacctsubtype: ${NEO_DEFAULT_ACWACCTSUBTYPE:TRUE}



cai3g:
  location: ire
  trustStore: classpath:config/keys/windtre_eda.jks
  tspassword: windtre
  #user: wing
  #password: Wing001!
  user: WINGSDM
  password: WingSDM@Prov1
  url: https://localhost:8181/CAI3G1.2/services/CAI3G1.2
  #hlrUrl: https://localhost:8181/WING/Dispatcher
  #hssUrl: https://localhost:8181/WING/Dispatcher
  hlrUrl: https://localhost:8181/CAI3G1.2/services/CAI3G1.2
  hssUrl: https://localhost:8181/CAI3G1.2/services/CAI3G1.2
  sessionPoolSize: 5
  #hlrMoType: WINGSubscription@http://schemas.ericsson.com/ma/CA/WINGSubscription/
  #hssMoType: WINGEPSMultiSC@http://schemas.ericsson.com/ma/CA/WINGEPSMultiSC/
  hlrMoType: Subscription@http://schemas.ericsson.com/ema/UserProvisioning/GsmHlr/
  hssMoType: EPSMultiSC@http://schemas.ericsson.com/ma/HSS/
  aucMoType: Subscription@http://schemas.ericsson.com/ema/UserProvisioning/GsmAuc/
  http:
    maxConnections: 25
    maxConnectionsPerRoute: 15
    connectionTimeOut: 1200
  camel:
    tdps:
      -
        serviceName: 222886-orig-trig
        csiState: 1
        csiNotify: 0
        triggeringPoint: 0
        detectionPoint: 2
        gsmScfAddress: 393205800606
        serviceKey: 52447
        defaultErrorHandling: 1
        cch: 1
        i: true
        dialnum:
      -
        serviceName: 222886-term-trig
        csiState: 1
        csiNotify: 0
        triggeringPoint: 1
        detectionPoint: 12
        gsmScfAddress: 393205800606
        serviceKey: 53447
        defaultErrorHandling: 1
        cch: 2
        i: true
        dialnum:


miscellaneous:
  url: https://csn-att-pp.ps.gi-de.com
  ota1: https://csn-att-pp.ps.gi-de.com/api/v1/update
  ota2:

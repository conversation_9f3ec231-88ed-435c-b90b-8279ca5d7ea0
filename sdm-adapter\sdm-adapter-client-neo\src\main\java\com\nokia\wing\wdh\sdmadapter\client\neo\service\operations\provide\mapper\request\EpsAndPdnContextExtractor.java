package com.nokia.wing.wdh.sdmadapter.client.neo.service.operations.provide.mapper.request;

import com.nokia.wing.wdh.sdmadapter.common.dto.subscriber.DataServiceDto;
import com.nokia.wing.wdh.sdmadapter.common.dto.subscriber.EpsPdnContextDto;
import com.nokia.wing.wdh.sdmadapter.common.dto.subscriber.PdnContextDto;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * Utility for extracting data from EPS and PDN contexts with priority logic.
 *
 * Follows EPS -> PDN priority logic and returns comma-separated values.
 * Handles errors gracefully by logging and returning null.
 *
 * This class specifically handles EPS PDN Context and PDN Context extraction patterns.
 */
@Slf4j
public class EpsAndPdnContextExtractor {

    /**
     * Extract values from contexts with EPS -> PDN priority
     *
     * @param dataService The data service containing contexts
     * @param epsExtractor Function to extract value from EPS context
     * @param pdnExtractor Function to extract value from PDN context
     * @param fieldName Name of field being extracted (for logging)
     * @param imsi IMSI for logging context
     * @return Comma-separated values or null if none found
     */
    public static <T> String extract(DataServiceDto dataService,
                                   Function<EpsPdnContextDto, T> epsExtractor,
                                   Function<PdnContextDto, T> pdnExtractor,
                                   String fieldName,
                                   String imsi) {
        if (dataService == null) {
            log.debug("DataService is null for IMSI {}, cannot extract {}", imsi, fieldName);
            return null;
        }

        List<String> values = new ArrayList<>();

        try {
            // Try EPS contexts first
            if (dataService.getEpsPdnContexts() != null) {
                for (EpsPdnContextDto context : dataService.getEpsPdnContexts()) {
                    if (context != null) {
                        T value = epsExtractor.apply(context);
                        if (isValidValue(value)) {
                            values.add(value.toString().trim());
                        }
                    }
                }
            }

            // Fallback to PDN contexts if no EPS values found
            if (values.isEmpty() && dataService.getPdnContexts() != null) {
                for (PdnContextDto context : dataService.getPdnContexts()) {
                    if (context != null) {
                        T value = pdnExtractor.apply(context);
                        if (isValidValue(value)) {
                            values.add(value.toString().trim());
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.warn("Error extracting {} for IMSI {}: {}", fieldName, imsi, e.getMessage());
            return null;
        }

        if (values.isEmpty()) {
            log.debug("No {} values found for IMSI {}", fieldName, imsi);
            return null;
        }

        String result = String.join(",", values);
        log.debug("Extracted {} for IMSI {}: {}", fieldName, imsi, result);
        return result;
    }

    private static <T> boolean isValidValue(T value) {
        return value != null && !value.toString().trim().isEmpty();
    }

    // Convenience methods for common extractions
    public static String extract5gQosProfile(DataServiceDto dataService, String imsi) {
        return extract(dataService,
            EpsPdnContextDto::getFive_qi_qci,
            PdnContextDto::getFive_qi_qci,
            "5G QoS Profile", imsi);
    }

    public static String extractArpPriorityLevel(DataServiceDto dataService, String imsi) {
        return extract(dataService,
            EpsPdnContextDto::getEarp,
            PdnContextDto::getEarp,
            "ARP Priority Level", imsi);
    }

    public static String extractPriorityLevel(DataServiceDto dataService, String imsi) {
        return extract(dataService,
            EpsPdnContextDto::getEarp,
            PdnContextDto::getEarp,
            "Priority Level", imsi);
    }

    public static String extractPdpName(DataServiceDto dataService, String imsi) {
        return extract(dataService,
            EpsPdnContextDto::getPdpName,
            PdnContextDto::getPdpName,
            "PDP Name", imsi);
    }

    public static String extractDnnName(DataServiceDto dataService, String imsi) {
        return extract(dataService,
            EpsPdnContextDto::getApn,
            PdnContextDto::getApn,
            "DNN Name", imsi);
    }
    public static String extractSessionAmbrUpLink(DataServiceDto dataService, String imsi) {
        return extract(dataService,
            EpsPdnContextDto::getSessionAmbrUpLink,
            PdnContextDto::getSessionAmbrUpLink,
            "SessionAmbr Up Link", imsi);
    }
    public static String extractSessionAmbrDownLink(DataServiceDto dataService, String imsi) {
        return extract(dataService,
            EpsPdnContextDto::getSessionAmbrDownLink,
            PdnContextDto::getSessionAmbrDownLink,
            "SessionAmbr Down Link", imsi);
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
           xmlns="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelSipTrunkingReferralService/"
           targetNamespace="http://schemas.ericsson.com/ema/UserProvisioning/MTAS/Service/MMTelSipTrunkingReferralService/"
           elementFormDefault="qualified" attributeFormDefault="unqualified" jaxb:extensionBindingPrefixes="xjc"
           jaxb:version="2.0">
	<xs:annotation>
		<xs:documentation xml:lang="en">
		MTAS 15A         	since MA15.0 CP1
			Added:
				SIP Trunking Referral Service		  
		</xs:documentation>
	</xs:annotation>
    <xs:element name="publicId" type="publicIdentityWTelType"/>
    <!-- MO Attributes for top level messages -->
    <xs:element name="createService">
        <xs:annotation>
            <xs:documentation xml:lang="en">Used to create MMTel group service data</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="publicId" type="publicIdentityWTelType">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">The default public user identity for the group. This identity
                            must already be configured on the HSS.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <!-- the relative order of the existing services must be maintained -->
                <!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
                <xs:element name="refer-to-identity" type="xs:anyURI" nillable="false" >
                    <xs:annotation>
                        <xs:documentation>Refer-to-identity element contains public identity of the associated service document. This element must be present and may never be withdrawn so it is not nillable.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdentityWTelType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="publicIdAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
        <xs:key name="publicIdKeyCreate">
            <xs:selector xpath="."/>
            <xs:field xpath="@publicId"/>
        </xs:key>
        <xs:keyref name="publicIdKeyRef" refer="publicIdKeyCreate">
            <xs:selector xpath="."/>
            <xs:field xpath="publicId"/>
        </xs:keyref>
    </xs:element>
    <xs:element name="setService">
        <xs:annotation>
            <xs:documentation xml:lang="en">Used to modify MMTel group service data</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="concurrency-control" type="xs:integer" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            The concurrency-control element is an optional element to control concurrent updates. If
                            present then the set
                            request will be accepted only if the service data version is still at the value given in
                            this element i.e. no other
                            updates have been performed. It is of type integer.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <!-- the relative order of the existing services must be maintained -->
                <!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
                <xs:element name="refer-to-identity" type="xs:anyURI" nillable="false" >
                    <xs:annotation>
                        <xs:documentation>Refer-to-identity element contains public identity of the associated service document. This element must be present and may never be withdrawn so it is not nillable.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdentityWTelType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="publicIdAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
        <xs:key name="publicIdKeySet">
            <xs:selector xpath="."/>
            <xs:field xpath="@publicId"/>
        </xs:key>
    </xs:element>
    <xs:element name="getResponseService">
        <xs:annotation>
            <xs:documentation xml:lang="en">Contains the currently configured MMTel group service data
            </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="publicId" type="publicIdentityWTelType">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">The default public user identity for the group
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="concurrency-control" type="xs:integer">
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            The concurrency-control element is an integer value indicating the current version of the
                            MMTel group service data.
                            This value can be used in a subsequent setMMTelGroup request to make sure that no changes
                            have been made to
                            the service data since the version that was read
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <!-- the relative order of the existing services must be maintained -->
                <!-- all new services shall be optional and inserted in alphabetical order within the existing list where possible -->
                <xs:element name="refer-to-identity" type="xs:anyURI" nillable="false" >
                    <xs:annotation>
                        <xs:documentation>Refer-to-identity element contains public identity of the associated service document. This element must be present and may never be withdrawn so it is not nillable.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
            <xs:attribute name="publicId" type="publicIdentityWTelType" use="required">
                <xs:annotation>
                    <xs:appinfo>
                        <jaxb:property name="publicIdAttr"/>
                    </xs:appinfo>
                </xs:annotation>
            </xs:attribute>
        </xs:complexType>
        <xs:key name="publicIdKeyGetResp">
            <xs:selector xpath="."/>
            <xs:field xpath="@publicId"/>
        </xs:key>
    </xs:element>

    <xs:simpleType name="publicIdentityWTelType">
		<xs:restriction base="xs:anyURI">
			<xs:pattern value="sip:.*|tel:.*!\.\*!"/>
		</xs:restriction>
    </xs:simpleType>
</xs:schema>

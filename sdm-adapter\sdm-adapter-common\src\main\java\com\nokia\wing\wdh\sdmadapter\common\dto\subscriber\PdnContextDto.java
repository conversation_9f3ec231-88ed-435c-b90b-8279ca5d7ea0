package com.nokia.wing.wdh.sdmadapter.common.dto.subscriber;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PdnContextDto {

	private Integer id;
	private PdnTypeDto type;//tell us which one is it
	private String qosProfile;
	private String apn;
	private String apnArea;
	private String address;//ipv4
	private String extAddress;//ipv6
	private Long qosProfileId;
    private Long apnId;
    //New parameters for Neo
    private String pdpName;
    private String earp;
    private String five_qi_qci;
    private String sessionAmbrUpLink;
    private String sessionAmbrDownLink;

}

<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ff98c478-647f-42d2-90dc-a281dfe19838" name="Changes" comment="Removed default Session Amber values and extracted from EPS and PDn context, reverted slice to NULL back">
      <change beforePath="$PROJECT_DIR$/sdm-adapter-client-neo/src/main/java/com/nokia/wing/wdh/sdmadapter/client/neo/config/interceptor/NeoSoapClientInterceptor.java" beforeDir="false" afterPath="$PROJECT_DIR$/sdm-adapter-client-neo/src/main/java/com/nokia/wing/wdh/sdmadapter/client/neo/config/interceptor/NeoSoapClientInterceptor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sdm-adapter-client-neo/src/main/java/com/nokia/wing/wdh/sdmadapter/client/neo/security/ssl/NeoSslConfigurationUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/sdm-adapter-client-neo/src/main/java/com/nokia/wing/wdh/sdmadapter/client/neo/security/ssl/NeoSslConfigurationUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sdm-adapter-client-neo/src/main/resources/config/application-neo.yml" beforeDir="false" afterPath="$PROJECT_DIR$/sdm-adapter-client-neo/src/main/resources/config/application-neo.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2zducKkZmoGAcI5p4YpseanEjiF" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.sdm-adapter [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.sdm-adapter-client-neo [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.sdm-adapter-common [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHELLCHECK.PATH&quot;: &quot;I do mind&quot;,
    &quot;Spring Boot.SDMSVCKAFKA.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.SDMSVCWithKAFKA.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.SdmAdapterServiceApp.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;feature/NeoAdapter&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;H:/ATT/code-base/ACM-oct/ACM%20Oct%20Launch/sdm-adapter&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;sdm-adapter-client-neo.wasFacetEverAdded&quot;: &quot;true&quot;,
    &quot;sdm-adapter-service.wasFacetEverAdded&quot;: &quot;true&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Spring Boot.SdmAdapterServiceApp">
    <configuration default="true" type="AZURE_FUNCTION_SUPPORT" factoryName="Run Functions">
      <option name="appName" />
      <option name="appServicePlanName" />
      <option name="appServicePlanResourceGroup" />
      <option name="appSettingsKey" value="b0b0dabf-50b0-4467-b2ff-313db8c1aa63" />
      <option name="artifact" />
      <option name="debugOptions" />
      <option name="deployment" />
      <option name="deploymentStagingDirectoryPath" />
      <option name="funcPath" />
      <option name="functionHostArguments" />
      <option name="hostJsonPath" />
      <option name="insightsName" />
      <option name="instrumentationKey" />
      <option name="javaVersion" />
      <option name="localSettingsJsonPath" />
      <option name="moduleName" />
      <option name="os" />
      <option name="pricingTier" />
      <option name="providerMap">
        <map />
      </option>
      <option name="region" />
      <option name="resourceGroup" />
      <option name="stagingFolder" />
      <option name="subscription" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SDMSVCKAFKA" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="ACTIVE_PROFILES" value="dev" />
      <envs>
        <env name="AZURE_CLIENT_ID" value="f6de8df4-9620-4aee-a856-e7c0f6f4a4e6" />
        <env name="AZURE_CLIENT_SECRET" value="****************************************" />
        <env name="AZURE_TENANT_ID" value="e741d71c-c6b6-47b0-803c-0f3b32b07556" />
        <env name="NEO_MESSAGING_ENABLED" value="true" />
        <env name="NEO_AUDIT_ENABLED" value="true" />
        <env name="KAFKA_BOOTSTRAP_SERVERS" value="localhost:9092" />
        <env name="NEO_AUDIT_KAFKA_TOPIC" value="wdh.sdm.adapter.neo.audit.log.events" />
      </envs>
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="sdm-adapter-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.nokia.wing.wdh.sdmadapter.service.SdmAdapterServiceApp" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SdmAdapterServiceApp" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="dev" />
      <envs>
        <env name="AZURE_CLIENT_ID" value="f6de8df4-9620-4aee-a856-e7c0f6f4a4e6" />
        <env name="AZURE_CLIENT_SECRET" value="****************************************" />
        <env name="AZURE_TENANT_ID" value="e741d71c-c6b6-47b0-803c-0f3b32b07556" />
      </envs>
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="sdm-adapter-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.nokia.wing.wdh.sdmadapter.service.SdmAdapterServiceApp" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.SdmAdapterServiceApp" />
      <item itemvalue="Spring Boot.SDMSVCKAFKA" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ff98c478-647f-42d2-90dc-a281dfe19838" name="Changes" comment="" />
      <created>1752073348412</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752073348412</updated>
      <workItem from="1752073349767" duration="4371000" />
      <workItem from="1752097884846" duration="5470000" />
      <workItem from="1752122117978" duration="8839000" />
      <workItem from="1752197102017" duration="6056000" />
      <workItem from="1752279628147" duration="24275000" />
      <workItem from="1752374795496" duration="14691000" />
      <workItem from="1752461550112" duration="5850000" />
      <workItem from="1752474463189" duration="1271000" />
      <workItem from="1752766893356" duration="1175000" />
      <workItem from="1752900148904" duration="3609000" />
      <workItem from="1752919487919" duration="10619000" />
      <workItem from="1753070842436" duration="3181000" />
      <workItem from="1753103431061" duration="3886000" />
      <workItem from="1753174558440" duration="2391000" />
    </task>
    <task id="LOCAL-00001" summary="Added Http header option">
      <option name="closed" value="true" />
      <created>1752077824549</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752077824549</updated>
    </task>
    <task id="LOCAL-00002" summary="updated dateTime">
      <option name="closed" value="true" />
      <created>1752126148888</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752126148888</updated>
    </task>
    <task id="LOCAL-00003" summary="updated download upload and updated calculateAcwsvcnetwork logic">
      <option name="closed" value="true" />
      <created>1752214020343</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752214020343</updated>
    </task>
    <task id="LOCAL-00004" summary="updated docs">
      <option name="closed" value="true" />
      <created>1752214825965</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752214825965</updated>
    </task>
    <task id="LOCAL-00005" summary="Added IMSI range based logic">
      <option name="closed" value="true" />
      <created>1752283978998</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752283978998</updated>
    </task>
    <task id="LOCAL-00006" summary="Added new parameters and prepwork for incorporating the logic">
      <option name="closed" value="true" />
      <created>1752296080923</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752296080923</updated>
    </task>
    <task id="LOCAL-00007" summary="Added new QOA parameter">
      <option name="closed" value="true" />
      <created>1752297583939</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752297583939</updated>
    </task>
    <task id="LOCAL-00008" summary="removed existing apn and pdp logic and lookup Logic">
      <option name="closed" value="true" />
      <created>1752298619798</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752298619798</updated>
    </task>
    <task id="LOCAL-00009" summary="added pdpname and dnn logic back">
      <option name="closed" value="true" />
      <created>1752303638147</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752303638147</updated>
    </task>
    <task id="LOCAL-00010" summary="Refactored mapper logic">
      <option name="closed" value="true" />
      <created>1752312294845</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752312294845</updated>
    </task>
    <task id="LOCAL-00011" summary="Handled exceptions in mapping">
      <option name="closed" value="true" />
      <created>1752379391051</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752379391051</updated>
    </task>
    <task id="LOCAL-00012" summary="Removed tests may look at it later">
      <option name="closed" value="true" />
      <created>1752387527864</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752387527864</updated>
    </task>
    <task id="LOCAL-00013" summary="Updated tokenRefreshBuffer">
      <option name="closed" value="true" />
      <created>1752389011537</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1752389011537</updated>
    </task>
    <task id="LOCAL-00014" summary="Removed default Session Amber values and extracted from EPS and PDn context, reverted slice to NULL back">
      <option name="closed" value="true" />
      <created>1753073869235</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753073869235</updated>
    </task>
    <option name="localTasksCounter" value="15" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Added Http header option" />
    <MESSAGE value="updated dateTime" />
    <MESSAGE value="updated download upload and updated calculateAcwsvcnetwork logic" />
    <MESSAGE value="updated docs" />
    <MESSAGE value="Added IMSI range based logic" />
    <MESSAGE value="Added new parameters and prepwork for incorporating the logic" />
    <MESSAGE value="Added new QOA parameter" />
    <MESSAGE value="removed existing apn and pdp logic and lookup Logic" />
    <MESSAGE value="added pdpname and dnn logic back" />
    <MESSAGE value="Refactored mapper logic" />
    <MESSAGE value="Handled exceptions in mapping" />
    <MESSAGE value="Removed tests may look at it later" />
    <MESSAGE value="Updated tokenRefreshBuffer" />
    <MESSAGE value="Removed default Session Amber values and extracted from EPS and PDn context, reverted slice to NULL back" />
    <option name="LAST_COMMIT_MESSAGE" value="Removed default Session Amber values and extracted from EPS and PDn context, reverted slice to NULL back" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/sdm-adapter-client-neo/src/main/java/com/nokia/wing/wdh/sdmadapter/client/neo/service/shared/builder/AbstractRequestBuilder.java</url>
          <line>149</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>
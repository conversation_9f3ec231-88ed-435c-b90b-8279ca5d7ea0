package com.nokia.wing.wdh.sdmadapter.client.neo.service.operations.provide.mapper.request;

import com.nokia.wing.wdh.sdmadapter.client.neo.service.shared.handler.ServiceOrderRequestService;
import com.nokia.wing.wdh.sdmadapter.client.neo.util.NeoConstants;
import com.nokia.wing.wdh.sdmadapter.client.neo.service.operations.provide.builder.ProvideParameterBuilder;

import com.nokia.wing.wdh.sdmadapter.client.neo.auditlogs.util.NeoDateUtils;
import com.nokia.wing.wdh.sdmadapter.client.neo.auditlogs.dto.NeoMapperAuditInfoDTO;
import com.nokia.wing.wdh.sdmadapter.client.neo.auditlogs.dto.NeoRequestSummaryDTO;
import com.nokia.wing.wdh.sdmadapter.client.neo.auditlogs.util.NeoAuditUtils;
import com.nokia.wing.wdh.sdmadapter.client.neo.auditlogs.service.NeoPublishAuditEventService;
import com.nokia.wing.wdh.sdmadapter.client.neo.auditlogs.service.NeoAuditEventFactory;
import com.nokia.wing.wdh.sdmadapter.client.neo.auditlogs.dto.NeoAuditLogEventType;
import com.nokia.wing.wdh.sdmadapter.client.neo.auditlogs.dto.NeoPublishAuditLogEventsDTO;
import com.nokia.wing.wdh.sdmadapter.common.dto.subscriber.VoiceServiceDto;
import com.nokia.wing.wdh.sdmadapter.common.dto.subscriber.SmsServiceDto;
import com.nokia.wing.wdh.sdmadapter.common.dto.subscriber.DataServiceDto;
import com.nokia.wing.wdh.sdmadapter.common.dto.subscriber.PdnContextDto;
import com.nokia.wing.wdh.sdmadapter.common.dto.subscriber.EpsPdnContextDto;

import com.nokia.wing.wdh.sdmadapter.common.dto.subscriber.SubscriberServicesDto;
import com.nokia.wing.wdh.sdmadapter.client.neo.config.NeoClientProperties;
import com.nokia.wing.wdh.sdmadapter.client.neo.exception.NeoServiceValidationException;
import com.nokia.wing.wdh.sdmadapter.client.neo.exception.NeoDataExtractionException;
import com.nokia.wing.wdh.sdmadapter.client.neo.exception.NeoConfigurationException;

import lombok.extern.slf4j.Slf4j;
import com.nokia.wing.wdh.sdmadapter.client.neo.autogenerated.ossj.ordermanagement.CreateAndStartRequestByValueRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * NEO Provide Request Mapper - Maps SDM DTOs to NEO OSSJ PROVIDE requests
 *
 *
 * This mapper specifically handles PROVIDE operation requests, converting SDM adapter
 * DTOs to NEO-specific OSSJ service order requests using the refactored clean
 * architecture implementation.
 */
@Slf4j
@Component
@ConditionalOnProperty(value="application.clientType", havingValue="NEO")
public class NeoProvideRequestMapper {
    private final ServiceOrderRequestService serviceOrderRequestService;
    private final NeoClientProperties neoClientProperties;
    private final NeoPublishAuditEventService auditEventService; // Optional for audit logging
    private final NeoAuditEventFactory auditEventFactory; // Optional for creating audit events

    @Autowired
    public NeoProvideRequestMapper(ServiceOrderRequestService serviceOrderRequestService,
                                  NeoClientProperties neoClientProperties,
                                  @Autowired(required = false) NeoPublishAuditEventService auditEventService,
                                  @Autowired(required = false) NeoAuditEventFactory auditEventFactory) {
        this.serviceOrderRequestService = serviceOrderRequestService;
        this.neoClientProperties = neoClientProperties;
        this.auditEventService = auditEventService;
        this.auditEventFactory = auditEventFactory;
    }

    /**
     * Creates mapper audit information for tracking mapper execution
     */
    private NeoMapperAuditInfoDTO createMapperAuditInfo(String method, String phase, boolean successful,
                                                    String errorMessage, int parametersProcessed) {
        return NeoMapperAuditInfoDTO.builder()
            .mapperClass("NeoProvideRequestMapper")
            .mapperMethod(method)
            .mappingPhase(phase)
            .mappingSuccessful(successful)
            .exceptionMessage(errorMessage)
            .parametersProcessed(parametersProcessed)
            .build();
    }

    /**
     * Publishes mapper audit event if audit services are available
     */
    private void publishMapperAuditEvent(NeoMapperAuditInfoDTO mapperInfo, String imsi,
                                       NeoRequestSummaryDTO requestSummary, boolean isError) {
        if (auditEventService != null && auditEventFactory != null) {
            try {
                NeoPublishAuditLogEventsDTO auditEvent;

                if (isError) {
                    // Create error audit event for mapping failures
                    auditEvent = auditEventFactory.createEnhancedErrorEvent(
                        NeoAuditLogEventType.MAPPING_ERROR,
                        "Request mapping " + (mapperInfo.isMappingSuccessful() ? "completed" : "failed") + " for IMSI: " + imsi,
                        new org.springframework.http.HttpHeaders(), // No HTTP context in mapper
                        mapperInfo.getExceptionMessage() != null ? new RuntimeException(mapperInfo.getExceptionMessage()) : null,
                        mapperInfo,
                        null, // No processing time at mapper level
                        NeoAuditUtils.generateCorrelationId()
                    );
                } else {
                    // Create enhanced request audit event for successful mapping
                    auditEvent = auditEventFactory.createEnhancedRequestEvent(
                        NeoAuditLogEventType.PROVIDE,
                        "Request mapping completed successfully for IMSI: " + imsi,
                        new org.springframework.http.HttpHeaders(), // No HTTP context in mapper
                        mapperInfo,
                        requestSummary
                    );
                }

                auditEventService.publishEvent(auditEvent);
                log.debug("NEO Request Mapper: Published audit event for mapping phase: {}, successful: {}",
                    mapperInfo.getMappingPhase(), mapperInfo.isMappingSuccessful());

            } catch (Exception e) {
                log.warn("NEO Request Mapper: Failed to publish mapper audit event for IMSI: {} - {}",
                    imsi, e.getMessage());
            }
        }
    }

    /**
     * Creates request summary for audit logging (lightweight representation)
     */
    private NeoRequestSummaryDTO createRequestSummary(String imsi, int parameterCount) {
        return NeoRequestSummaryDTO.builder()
            .imsi(imsi)
            .operationType("PROVIDE")
            .parameterCount(parameterCount)
            .requestId(NeoAuditUtils.generateRequestId(imsi, "PROVIDE"))
            .requestTimestamp(System.currentTimeMillis())
            .build();
    }

    /**
     * Creates request summary for audit logging after successful parameter mapping
     */
    public NeoRequestSummaryDTO createProvideRequestSummary(SubscriberServicesDto subscriberSvc, String imsi) {
        if (imsi == null || subscriberSvc == null) {
            return null;
        }

        try {
            // Get parameter count by doing a quick mapping (this could be optimized)
            Map<String, Object> parameters = mapSubscriberServicesToRequestParameters(subscriberSvc, imsi);
            return createRequestSummary(imsi, parameters.size());
        } catch (Exception e) {
            log.warn("Failed to create request summary for IMSI: {} - {}", imsi, e.getMessage());
            return createRequestSummary(imsi, 0);
        }
    }

    /**
     * Create comprehensive PROVIDE request from SubscriberServicesDto with simplified validation and parameter mapping
     */
    public Object createProvideRequest(SubscriberServicesDto subscriberSvc, String imsi) {
        // CRITICAL VALIDATION - Use custom exceptions for proper error handling
        if (imsi == null || imsi.trim().isEmpty()) {
            throw new NeoServiceValidationException("IMSI cannot be null or empty", null);
        }
        if (subscriberSvc == null) {
            throw new NeoServiceValidationException("SubscriberServicesDto cannot be null", imsi);
        }

        log.debug("Creating SOAP PROVIDE request for createAndActivateSim- IMSI: {}", imsi);

        try {
            log.debug("Starting simplified parameter mapping for IMSI: {}", imsi);

            // Extract parameters using simplified approach
            Map<String, Object> parameters = mapSubscriberServicesToRequestParameters(subscriberSvc, imsi);

            // Create PROVIDE request - wrap service layer exceptions simply
            CreateAndStartRequestByValueRequest request = serviceOrderRequestService.createProvideRequest(parameters);

            // Create mapper audit info for successful mapping
            NeoMapperAuditInfoDTO mapperInfo = createMapperAuditInfo(
                "createProvideRequest", "REQUEST_MAPPING", true, null, parameters.size());

            // Create request summary for audit logging
            NeoRequestSummaryDTO requestSummary = createRequestSummary(imsi, parameters.size());

            // Publish successful mapping audit event
            publishMapperAuditEvent(mapperInfo, imsi, requestSummary, false);

            log.info("Successfully created SOAP PROVIDE request for createAndActivateSim- IMSI: {}", imsi);
            log.debug("NEO Request Mapper: Successfully processed {} parameters for IMSI: {}",
                parameters.size(), imsi);

            return request;

        } catch (NeoServiceValidationException | NeoDataExtractionException | NeoConfigurationException e) {
            // Create mapper audit info for known exceptions
            NeoMapperAuditInfoDTO mapperInfo = createMapperAuditInfo(
                "createProvideRequest", "REQUEST_MAPPING", false, e.getMessage(), 0);

            // Publish error audit event for known exceptions
            publishMapperAuditEvent(mapperInfo, imsi, null, true);

            log.warn("NEO Request Mapper: Known exception during mapping for IMSI: {} - {}",
                imsi, e.getMessage());

            // Re-throw our custom exceptions
            throw e;
        } catch (Exception e) {
            // Create mapper audit info for unexpected exceptions
            NeoMapperAuditInfoDTO mapperInfo = createMapperAuditInfo(
                "createProvideRequest", "REQUEST_MAPPING", false, e.getMessage(), 0);

            // Publish error audit event for unexpected exceptions
            publishMapperAuditEvent(mapperInfo, imsi, null, true);

            log.error("Failed to create SOAP PROVIDE request for createAndActivateSim - IMSI: {}", imsi, e.getMessage(), e);
            throw new NeoDataExtractionException("Failed to create SOAP request", imsi, e);
        }
    }

    /**
     * Maps SubscriberServicesDto to NEO request parameters using simplified approach with ParameterBuilder
     */
    private Map<String, Object> mapSubscriberServicesToRequestParameters(SubscriberServicesDto subscriberSvc, String imsi) {
        log.debug("Starting simplified parameter mapping for IMSI: {}", imsi);

        // Cache IMSI prefix check for performance (simple improvement)
        boolean hasConfiguredPrefix = hasConfiguredImsiPrefix(imsi);

        // Use ProvideParameterBuilder for cleaner parameter management
        ProvideParameterBuilder builder = new ProvideParameterBuilder(imsi);

        // === PHASE 1: BASIC IDENTITY EXTRACTION ===
        log.debug("Phase 1 - Basic identity extraction for IMSI: {}", imsi);
        extractBasicIdentity(builder, subscriberSvc, imsi);

        // === PHASE 2: SERVICE CHARACTERISTICS EXTRACTION ===
        log.debug("Phase 2 - Service characteristics extraction for IMSI: {}", imsi);
        extractServiceCharacteristics(builder, subscriberSvc, imsi);

        // === PHASE 3: DYNAMIC CALCULATIONS ===
        log.debug("Phase 3 - Dynamic calculations for IMSI: {}", imsi);
        calculateDynamicParameters(builder, subscriberSvc, imsi, hasConfiguredPrefix);

        // === PHASE 4: CONFIGURATION AND DEFAULTS ===
        log.debug("Phase 4 - Configuration and defaults for IMSI: {}", imsi);
        mapConfigurationParameters(builder);

        Map<String, Object> result = builder.build();
        log.info("Parameter mapping completed for IMSI {}: {} parameters added", imsi, builder.getSuccessCount());

        return result;
    }

    // ========================================
    // PHASE 1: BASIC IDENTITY EXTRACTION
    // ========================================

    /**
     * Extract basic subscriber identity parameters using ProvideParameterBuilder
     */
    private void extractBasicIdentity(ProvideParameterBuilder builder, SubscriberServicesDto subscriberSvc, String imsi) {
        log.debug("Processing basic identity parameters for IMSI: {}", imsi);

        builder
            .add(NeoConstants.IMSI, imsi)
            .add(NeoConstants.SUBSCRIPTION_ID, subscriberSvc.getId())
            .add(NeoConstants.MSISDN, subscriberSvc.getMsisdn());
            //.add(NeoConstants.PRIMARY_PUID, subscriberSvc.getMsisdn()); Removed as per new implementation

        log.debug("Basic identity extraction completed for IMSI: {}", imsi);
    }

    // ========================================
    // PHASE 2: SERVICE CHARACTERISTICS EXTRACTION
    // ========================================

    /**
     * Extract all service characteristics using ProvideParameterBuilder and EpsAndPdnContextExtractor
     */
    private void extractServiceCharacteristics(ProvideParameterBuilder builder, SubscriberServicesDto subscriberSvc, String imsi) {
        log.debug("Processing service characteristics for IMSI: {}", imsi);

        // CRITICAL VALIDATION: Check for required services
        if (subscriberSvc.getVoiceService() == null) {
            throw new NeoServiceValidationException("Voice service is null - cannot process PROVIDE request", imsi);
        }
        if (subscriberSvc.getSmsService() == null) {
            throw new NeoServiceValidationException("SMS service is null - cannot process PROVIDE request", imsi);
        }
        if (subscriberSvc.getDataService() == null) {
            throw new NeoServiceValidationException("Data service is null - cannot process PROVIDE request", imsi);
        }

        // SMS Service
        if (subscriberSvc.getSmsService() != null) {
            SmsServiceDto sms = subscriberSvc.getSmsService();
            boolean hasMtMsisdns = sms.getMtMsisdns() != null && !sms.getMtMsisdns().isEmpty();
            boolean hasMoMsisdns = sms.getMoMsisdns() != null && !sms.getMoMsisdns().isEmpty();

            builder
                .addIf(NeoConstants.SMS, neoClientProperties.getDefaults().getSms(), hasMtMsisdns || hasMoMsisdns)
                .add(NeoConstants.IS_BAR_OUTGOING_SMS, NeoConstants.toNeoBoolean(sms.getMoBarred()))
                .add(NeoConstants.IS_BAR_INCOMING_SMS, NeoConstants.toNeoBoolean(sms.getMtBarred()))
                .add(NeoConstants.IS_BAR_OUTGOING_INTERNATIONAL_SMS, NeoConstants.toNeoBoolean(sms.getSmsBoiexh()));
        }

        // Voice Service
        if (subscriberSvc.getVoiceService() != null) {
            VoiceServiceDto voice = subscriberSvc.getVoiceService();
            builder
                .add(NeoConstants.IS_BAR_INCOMING_CALLS, NeoConstants.toNeoBoolean(voice.getMtBarred(), NeoConstants.BOOLEAN_TRUE))
                .add(NeoConstants.IS_BAR_OUTGOING_CALLS, NeoConstants.toNeoBoolean(voice.getMoBarred(), NeoConstants.BOOLEAN_TRUE));
        }

        // Data Service - using ContextExtractor to eliminate duplication
        if (subscriberSvc.getDataService() != null) {
            DataServiceDto dataService = subscriberSvc.getDataService();

            builder
                .add(NeoConstants.ROAMING_RESTRICTION, extractRoamingProfile(dataService))
                .add(NeoConstants.GPRS_ROAMING_RESTRICTION, extractGprsRestriction(dataService))
                .add(NeoConstants.PDP_NAME, EpsAndPdnContextExtractor.extractPdpName(dataService, imsi));

            // Calculate default PDP name
            String defaultPdpName = calculateDefaultPdpName(subscriberSvc);
            builder.add(NeoConstants.DEFAULT_PDP_NAME, defaultPdpName);
        }

        log.debug("Service characteristics extraction completed for IMSI: {}", imsi);
    }

    // ========================================
    // PHASE 3: DYNAMIC CALCULATIONS
    // ========================================
    /**
     * Simplified dynamic calculations using ProvideParameterBuilder and cached IMSI prefix
     */
    private void calculateDynamicParameters(ProvideParameterBuilder builder, SubscriberServicesDto subscriberSvc,
                                          String imsi, boolean hasConfiguredPrefix) {
        NeoClientProperties.Defaults imsiDependentDefaults = neoClientProperties.getDefaults();
        // Network Access Mode
        String networkAccessMode = calculateNetworkAccessModeFromServices(subscriberSvc);
        builder.add(NeoConstants.NETWORK_ACCESS_MODE, networkAccessMode);

        // ACWSVCNETWORK (omit if NULL)
        String acwsvcnetwork = calculateAcwsvcnetwork(subscriberSvc, imsi);
        if (!NeoConstants.ACWSVCNETWORK_NULL.equals(acwsvcnetwork)) {
            builder.add(NeoConstants.ACWSVCNETWORK, acwsvcnetwork);
        }

        // HSS Pool ID and other IMSI-dependent parameters using cached prefix check
        builder
            .add(NeoConstants.HSS_POOL_ID, hasConfiguredPrefix ? NeoConstants.HSA : NeoConstants.HSS_DEFAULT)
            .add(NeoConstants.BASIC_MSISDN_BC_NAME, hasConfiguredPrefix ? NeoConstants.BASIC_MSISDN_BC_NAME_NORTH_RIM : NeoConstants.BASIC_MSISDN_BC_NAME_DEFAULT)
            .add(NeoConstants.CORE_IND, hasConfiguredPrefix ? NeoConstants.CORE_IND_IMSI_SPECIFIC : NeoConstants.CORE_IND_DEFAULT);

        // IP Messaging Indicator
        String ipMessagingIndicator = calculateIpMessagingIndicator(subscriberSvc);
        builder.add(NeoConstants.IP_MESSAGING_INDICATOR, ipMessagingIndicator);

        // Effective date
        String effectiveDate = NeoDateUtils.getNeoEffectiveDate();
        builder.add(NeoConstants.EFFECTIVE_DATE, effectiveDate);

        // IMSI prefix dependent parameters (no dataService dependency)
        if (hasConfiguredPrefix) {
            builder
                .add(NeoConstants.DOWNLOAD_SPEED, NeoConstants.DOWNLOAD_SPEED_DEFAULT)
                .add(NeoConstants.UPLOAD_SPEED, NeoConstants.UPLOAD_SPEED_DEFAULT)
                .add(NeoConstants.DEFAULT_SSC_MODE, NeoConstants.DEFAULT_SSC_MODE_DEFAULT)
                .add(NeoConstants.SLICE_SERVICE_TYPE, NeoConstants.SLICE_SERVICE_TYPE_DEFAULT)
                .add(NeoConstants.SLICE_DIFFERENTIATOR, NeoConstants.SLICE_DIFFERENTIATOR_DEFAULT)
                .add(NeoConstants.CHARGING_CHARACTERISTIC, imsiDependentDefaults.getChargingCharacteristic())
                .add(NeoConstants.ARP_PREEMPT_CAP, imsiDependentDefaults.getArpPreemptCap())
                .add(NeoConstants.ARP_PREEMPT_VULN, imsiDependentDefaults.getArpPreemptVuln())
                .add(NeoConstants.IWK_EPS_IND, imsiDependentDefaults.getIwkEpsInd())
                .add(NeoConstants.DEFAULT_DNN_NAME, subscriberSvc.getDefaultApnName());

            // Session types , we have added here for null saftey
            String sessionTypes = calculateSessionTypes(subscriberSvc);
            builder
                .add(NeoConstants.ALLOWED_SESSION_TYPES, sessionTypes)
                .add(NeoConstants.DEFAULT_SESSION_TYPES, sessionTypes);
        }else{
            builder
                .add(NeoConstants.OTA_VALIDITY, imsiDependentDefaults.getOtaValidity());
        }

        // IMSI prefix + DataService dependent parameters
        if (hasConfiguredPrefix && subscriberSvc.getDataService() != null) {
            DataServiceDto dataService = subscriberSvc.getDataService();
            builder
                .add(NeoConstants.DNN_NAME, EpsAndPdnContextExtractor.extractDnnName(dataService, imsi))
                .add(NeoConstants.QOS_PROFILE_5G, EpsAndPdnContextExtractor.extract5gQosProfile(dataService, imsi))
                .add(NeoConstants.ARP_PRIORITY_LEVEL, EpsAndPdnContextExtractor.extractArpPriorityLevel(dataService, imsi))
                .add(NeoConstants.PRIORITY_LEVEL, EpsAndPdnContextExtractor.extractPriorityLevel(dataService, imsi))
                .add(NeoConstants.SESSION_AMBR_DOWNLINK, EpsAndPdnContextExtractor.extractSessionAmbrDownLink(dataService, imsi))
                .add(NeoConstants.SESSION_AMBR_UPLINK, EpsAndPdnContextExtractor.extractSessionAmbrUpLink(dataService, imsi));
        }


        log.debug("Dynamic calculations completed for IMSI: {}", imsi);
    }

    /**
     * Simplified configuration mapping using ProvideParameterBuilder
     */
    private void mapConfigurationParameters(ProvideParameterBuilder builder) {
        log.debug("Processing configuration parameters");

        // CRITICAL VALIDATION: Configuration properties are required
        if (neoClientProperties == null) {
            throw new NeoConfigurationException("NeoClientProperties is null - cannot process PROVIDE request");
        }
        if (neoClientProperties.getDefaults() == null) {
            throw new NeoConfigurationException("NeoClientProperties defaults are null - cannot process PROVIDE request");
        }

        NeoClientProperties.Defaults defaults = neoClientProperties.getDefaults();

        // Map all configuration properties with null safety
        builder
            .add(NeoConstants.MARKET, defaults.getMarket())
            .add(NeoConstants.SUBMARKET, defaults.getSubMarket())
            .add(NeoConstants.HOME_SMSC, defaults.getMarket())
            .add(NeoConstants.OPERATOR_ID, defaults.getOperatorId())
            .add(NeoConstants.ORIGINATING_CLASS_OF_SERVICE, defaults.getOriginatingClassOfService())
            .add(NeoConstants.CALLER_ID, defaults.getCallerId())
            .add(NeoConstants.EMLPP_ACTIVE, defaults.getEmlppActive())
            .add(NeoConstants.SUBSCRIPTION_CLASS, defaults.getSubscriptionClass())
            .add(NeoConstants.CLIENT_APPLICATION_ID, defaults.getClientApplicationId())
            .add(NeoConstants.CARRIER_ID, defaults.getCarrierId())
            .add(NeoConstants.EQUIPMENT_TYPE, defaults.getEquipmentType())
            .add(NeoConstants.EXTERNAL_OFFER_ID, defaults.getExternalOfferId())
            .add(NeoConstants.OFFER_LEVEL, defaults.getOfferLevel())
            .add(NeoConstants.ACWACCTSUBTYPE, defaults.getAcwacctsubtype());
            //.add(NeoConstants.VIDEO_STREAMING_QUALITY, defaults.getVideoStreamingQuality()) // Removed as per new implementation
            //.add(NeoConstants.CHARGING_CHARACTERISTIC, defaults.getChargingCharacteristic()) //moved to imsi-prefix logic
            //.add(NeoConstants.ARP_PREEMPT_CAP, defaults.getArpPreemptCap()) // moved to imsi-prefix logic
            //.add(NeoConstants.ARP_PREEMPT_VULN, defaults.getArpPreemptVuln()) // moved to imsi-prefix logic
            //.add(NeoConstants.IWK_EPS_IND, defaults.getIwkEpsInd()); // moved to imsi-prefix logic

        // Add hardcoded default values
        builder
            .add(NeoConstants.MMS, NeoConstants.MMS_DEFAULT)
            .add(NeoConstants.PAYMENT_TYPE, NeoConstants.PAYMENT_TYPE_DEFAULT)
            .add(NeoConstants.IS_BAR_WAP, NeoConstants.IS_BAR_WAP_DEFAULT)
            .add(NeoConstants.CONTENT_FILTER, NeoConstants.CONTENT_FILTER_DEFAULT)
            .add(NeoConstants.IS_AGE_VERIFIED, NeoConstants.IS_AGE_VERIFIED_DEFAULT)
            .add(NeoConstants.NR_AS_SECONDARY_RAT_ALLOWED, NeoConstants.NR_AS_SECONDARY_RAT_ALLOWED_DEFAULT)
            .add(NeoConstants.CLIP_ACT, NeoConstants.CLIP_ACT_DEFAULT)
            .add(NeoConstants.CLIP_AUTH, NeoConstants.CLIP_AUTH_DEFAULT)
            .add(NeoConstants.SUBS_USSD_ENABLED, NeoConstants.SUBS_USSD_ENABLED_DEFAULT)
            .add(NeoConstants.MPTY_AUTH, NeoConstants.MPTY_AUTH_DEFAULT)
            .add(NeoConstants.MPTY_ACT, NeoConstants.MPTY_ACT_DEFAULT)
            .add(NeoConstants.HOLD_AUTH, NeoConstants.HOLD_AUTH_DEFAULT)
            .add(NeoConstants.HOLD_ACT, NeoConstants.HOLD_ACT_DEFAULT)
            .add(NeoConstants.PASSWORD_OPTION_FOR_SDB, NeoConstants.PASSWORD_OPTION_FOR_SDB_DEFAULT)
            .add(NeoConstants.CFB_AUTH, NeoConstants.CFB_AUTH_DEFAULT)
            .add(NeoConstants.CFNRC_AUTH, NeoConstants.CFNRC_AUTH_DEFAULT)
            .add(NeoConstants.CFNRY_AUTH, NeoConstants.CFNRY_AUTH_DEFAULT)
            .add(NeoConstants.CFD_AUTH, NeoConstants.CFD_AUTH_DEFAULT)
            .add(NeoConstants.TELE_CFD_ACT, NeoConstants.TELE_CFD_ACT_DEFAULT)
            .add(NeoConstants.TELE_CFD_FTN_ADDR, NeoConstants.TELE_CFD_FTN_ADDR_DEFAULT)
            .add(NeoConstants.TELE_CFD_FTN_SUB_ADDR, NeoConstants.TELE_CFD_FTN_SUB_ADDR_DEFAULT)
            .add(NeoConstants.TELE_CFB_NOTIF_CALL_PARTY, NeoConstants.TELE_CFB_NOTIF_CALL_PARTY_DEFAULT)
            .add(NeoConstants.TELE_CFNRC_NOTIF_CALL_PARTY, NeoConstants.TELE_CFNRC_NOTIF_CALL_PARTY_DEFAULT)
            .add(NeoConstants.TELE_CFNRY_NOTIF_CALL_PARTY, NeoConstants.TELE_CFNRY_NOTIF_CALL_PARTY_DEFAULT)
            .add(NeoConstants.CLIR_ACT, NeoConstants.CLIR_ACT_DEFAULT)
            .add(NeoConstants.CLIR_AUTH, NeoConstants.CLIR_AUTH_DEFAULT)
            .add(NeoConstants.IS_INTERNATIONAL, NeoConstants.IS_INTERNATIONAL_DEFAULT)
            .add(NeoConstants.GSM_SUBSCRIPT_RESTRICT, NeoConstants.GSM_SUBSCRIPT_RESTRICT_DEFAULT)
            .add(NeoConstants.CW_AUTH, NeoConstants.CW_AUTH_DEFAULT)
            .add(NeoConstants.CW_ACT, NeoConstants.CW_ACT_DEFAULT)
            //.add(NeoConstants.DEFAULT_SSC_MODE, NeoConstants.DEFAULT_SSC_MODE_DEFAULT) //moved to imsi-prefix logic
            //.add(NeoConstants.SLICE_SERVICE_TYPE, NeoConstants.SLICE_SERVICE_TYPE_DEFAULT) //moved to imsi-prefix logic
            //.add(NeoConstants.SLICE_DIFFERENTIATOR, NeoConstants.SLICE_DIFFERENTIATOR_DEFAULT) //moved to imsi-prefix logic
            .add(NeoConstants.TELE_CFU_ACT, NeoConstants.TELE_CFU_ACT_DEFAULT)
            .add(NeoConstants.CFU_AUTH, NeoConstants.CFU_AUTH_DEFAULT)
            .add(NeoConstants.INTERZONAL_INTL_OG_NOT_HPLMN, NeoConstants.INTERZONAL_INTL_OG_NOT_HPLMN_DEFAULT)
            .add(NeoConstants.INTL_OG_CALLS_NOT_HPLMN, NeoConstants.INTL_OG_CALLS_NOT_HPLMN_DEFAULT);

        log.debug("Configuration parameters mapping completed");
    }

    // ========================================
    // CALCULATION METHODS
    // ========================================

    /**
     * Calculates network access mode based on available services
     */
    private String calculateNetworkAccessModeFromServices(SubscriberServicesDto subscriberSvc) {
        try {
            boolean hasVoice = subscriberSvc.getVoiceService() != null;
            boolean hasSms = subscriberSvc.getSmsService() != null;
            boolean hasData = subscriberSvc.getDataService() != null;

            if (hasVoice && hasSms && hasData) {
                log.debug("Dynamic mapping: Full service detected - voice, SMS, and data available");
                return NeoConstants.NETWORK_ACCESS_MODE_VOICE_SMS_DATA; // "00"
            } else if (hasData && (!hasVoice || !hasSms)) {
                log.debug("Dynamic mapping: Data only/partial service detected");
                return NeoConstants.NETWORK_ACCESS_MODE_DATA_ONLY; // "00"
            } else if (hasVoice && !hasSms && !hasData) {
                log.debug("Dynamic mapping: Voice only service detected");
                return NeoConstants.NETWORK_ACCESS_MODE_VOICE_ONLY; // "01"
            } else {
                throw new NeoDataExtractionException("Unknown service combination - cannot determine network access mode", null);
            }
        } catch (NeoDataExtractionException e) {
            // Re-throw our custom exceptions
            throw e;
        } catch (Exception e) {
            throw new NeoDataExtractionException("Error determining network access mode", null, e);
        }
    }

    /**
     * Calculate Default PDP Name by looking up the default APN in EPS PDN contexts.
     *
     * Logic:
     * 1. Get defaultApn from SubscriberServicesDto.getDefaultApnName()
     * 2. Look through SubscriberServicesDto.getDataService().getEpsPdnContexts() list
     * 3. Find the EpsPdnContextDto where apn matches the defaultApn
     * 4. Extract the pdpName from that matching EpsPdnContextDto
     * 5. Return the pdpName as defaultPDPName
     *
     * @param subscriberSvc The subscriber services DTO containing default APN and EPS PDN contexts
     * @return The pdpName from the matching EPS PDN context
     * @throws NeoDataExtractionException if default APN is null/empty, no matching EPS PDN context found,
     *                                   or matching context has null/empty pdpName
     */
    private String calculateDefaultPdpName(SubscriberServicesDto subscriberSvc) {
        try {
            log.debug("Dynamic mapping: Calculating Default PDP Name from default APN lookup in EPS PDN contexts");

            // Step 1: CRITICAL VALIDATION - Default APN name is required
            if (subscriberSvc == null) {
                throw new NeoDataExtractionException("SubscriberServicesDto is null - cannot calculate default PDP name", null);
            }
            if (subscriberSvc.getDefaultApnName() == null || subscriberSvc.getDefaultApnName().trim().isEmpty()) {
                throw new NeoDataExtractionException("Default APN name is null or empty - cannot calculate default PDP name", null);
            }

            String defaultApn = subscriberSvc.getDefaultApnName().trim();
            log.debug("Dynamic mapping: Default APN name: {}", defaultApn);

            // Step 2: CRITICAL VALIDATION - EPS PDN contexts are required for default PDP name calculation
            if (subscriberSvc.getDataService() == null) {
                throw new NeoDataExtractionException("Data service is null - cannot calculate default PDP name", null);
            }
            if (subscriberSvc.getDataService().getEpsPdnContexts() == null ||
                subscriberSvc.getDataService().getEpsPdnContexts().isEmpty()) {
                throw new NeoDataExtractionException("EPS PDN contexts are null or empty - cannot calculate default PDP name", null);
            }

            // Step 3: Look through EPS PDN contexts to find matching APN
            for (EpsPdnContextDto epsPdnContext : subscriberSvc.getDataService().getEpsPdnContexts()) {
                if (epsPdnContext != null && epsPdnContext.getApn() != null &&
                    !epsPdnContext.getApn().trim().isEmpty()) {

                    String contextApn = epsPdnContext.getApn().trim();
                    log.debug("Dynamic mapping: Checking EPS PDN context APN: {}", contextApn);

                    // Step 4: Check if this APN matches the default APN
                    if (defaultApn.equals(contextApn)) {
                        log.debug("Dynamic mapping: Found matching EPS PDN context for default APN: {}", defaultApn);

                        // Step 5: Extract pdpName from the matching context
                        if (epsPdnContext.getPdpName() != null && !epsPdnContext.getPdpName().trim().isEmpty()) {
                            String defaultPdpName = epsPdnContext.getPdpName().trim();
                            log.debug("Dynamic mapping: Found Default PDP Name: {}", defaultPdpName);
                            return defaultPdpName;
                        } else {
                            throw new NeoDataExtractionException("Matching EPS PDN context found but pdpName is null or empty for APN: " + defaultApn, null);
                        }
                    }
                }
            }

            // No matching EPS PDN context found
            throw new NeoDataExtractionException("No EPS PDN context found with APN matching default APN: " + defaultApn, null);

        } catch (NeoDataExtractionException e) {
            // Re-throw our custom exceptions
            throw e;
        } catch (Exception e) {
            throw new NeoDataExtractionException("Error calculating Default PDP Name", null, e);
        }
    }

    /**
     * Calculate ACWSVCNETWORK value based on IMSI prefix and service availability.
     *
     * Logic Priority:
     * 1. If IMSI starts with configured prefix → "5gsaphone"
     * 2. If both 5G NSA data AND voice MSISDNs exist → "5gphone"
     * 3. If 5G NSA exists BUT no voice MSISDNs → "5gbasic"
     * 4. If no 5G NSA → "NULL" (parameter will be omitted from SOAP XML)
     *
     * Note: When the result is "NULL", the calling method will omit this parameter
     * entirely from the SOAP request rather than sending "NULL" as the value.
     *
     * @param subscriberSvc The subscriber services DTO containing service data
     * @param imsi The subscriber's IMSI
     * @return The calculated ACWSVCNETWORK value or "NULL" to indicate parameter should be omitted
     */
    private String calculateAcwsvcnetwork(SubscriberServicesDto subscriberSvc, String imsi) {
        try {
            log.debug("Dynamic mapping: Calculating ACWSVCNETWORK for IMSI: {}", imsi);
            if (hasConfiguredImsiPrefix(imsi)) {
                log.debug("Dynamic mapping: IMSI matches configured prefix, returning '5gsaphone'");
                return NeoConstants.ACWSVCNETWORK_5GSA_PHONE;
            }
            boolean has5gNsa = has5gNsaData(subscriberSvc);
            boolean hasVoice = hasVoiceService(subscriberSvc);

            log.debug("Dynamic mapping: Service availability - 5G NSA: {}, Voice: {}", has5gNsa, hasVoice);

            if (has5gNsa && hasVoice) {
                log.debug("Dynamic mapping: Both 5G NSA and Voice available, returning '5gphone'");
                return NeoConstants.ACWSVCNETWORK_5G_PHONE;
            } else if (has5gNsa && !hasVoice) {
                log.debug("Dynamic mapping: 5G NSA available but no Voice, returning '5gbasic'");
                return NeoConstants.ACWSVCNETWORK_5G_BASIC;
            } else {
                log.debug("Dynamic mapping: No 5G NSA available, returning 'NULL'");
                return NeoConstants.ACWSVCNETWORK_NULL;
            }

        } catch (Exception e) {
            throw new NeoDataExtractionException("Error calculating ACWSVCNETWORK", imsi, e);
        }
    }

    /**
     * Check if IMSI starts with the configured prefix.
     *
     * @param imsi The subscriber's IMSI
     * @return true if IMSI starts with configured prefix, false otherwise
     */
    private boolean hasConfiguredImsiPrefix(String imsi) {
        try {
            if (imsi == null || imsi.trim().isEmpty()) {
                log.debug("Dynamic mapping: IMSI is null or empty, prefix check failed");
                return false;
            }

            if (neoClientProperties == null || neoClientProperties.getDefaults() == null) {
                log.debug("Dynamic mapping: NeoClientProperties or defaults is null, skipping prefix check");
                return false;
            }

            String configuredPrefix = neoClientProperties.getDefaults().getImsiPrefix();
            if (configuredPrefix == null || configuredPrefix.trim().isEmpty()) {
                log.debug("Dynamic mapping: No IMSI prefix configured, skipping prefix check");
                return false;
            }

            boolean matches = imsi.startsWith(configuredPrefix);
            log.debug("Dynamic mapping: IMSI '{}' prefix check against '{}': {}", imsi, configuredPrefix, matches);
            return matches;

        } catch (Exception e) {
            log.error("Dynamic mapping: Error checking IMSI prefix for IMSI: {}", imsi, e);
            return false;
        }
    }

    /**
     * Check if subscriber has 5G NSA data available.
     *
     * @param subscriberSvc The subscriber services DTO
     * @return true if 5G NSA data is available, false otherwise
     */
    private boolean has5gNsaData(SubscriberServicesDto subscriberSvc) {
        try {
            if (subscriberSvc == null) {
                log.debug("Dynamic mapping: SubscriberServicesDto is null, no 5G NSA data");
                return false;
            }

            boolean has5gNsa = subscriberSvc.getUdm5gData() != null &&
                              subscriberSvc.getUdm5gData().getImsi() != null;

            log.debug("Dynamic mapping: 5G NSA data availability: {}", has5gNsa);
            return has5gNsa;

        } catch (Exception e) {
            log.error("Dynamic mapping: Error checking 5G NSA data availability", e);
            return false;
        }
    }

    /**
     * Check if subscriber has voice service with MSISDNs available.
     *
     * @param subscriberSvc The subscriber services DTO
     * @return true if voice service with MSISDNs is available, false otherwise
     */
    private boolean hasVoiceService(SubscriberServicesDto subscriberSvc) {
        try {
            if (subscriberSvc == null || subscriberSvc.getVoiceService() == null) {
                log.debug("Dynamic mapping: No voice service available");
                return false;
            }

            boolean hasVoiceMsisdns = subscriberSvc.getVoiceService().getMsisdns() != null &&
                                     !subscriberSvc.getVoiceService().getMsisdns().isEmpty();

            log.debug("Dynamic mapping: Voice service MSISDNs availability: {}", hasVoiceMsisdns);
            return hasVoiceMsisdns;

        } catch (Exception e) {
            log.error("Dynamic mapping: Error checking voice service availability", e);
            return false;
        }
    }



    /**
     * Extract roaming profile from nested data services
     */
    private String extractRoamingProfile(DataServiceDto dataService) {
        try {
            // Try EPS data service first
            if (dataService.getEpsDataService() != null &&
                dataService.getEpsDataService().getRoamingProfile() != null) {
                return dataService.getEpsDataService().getRoamingProfile();
            }

            // Fall back to GPRS data service
            if (dataService.getGprsDataService() != null &&
                dataService.getGprsDataService().getRoamingProfile() != null) {
                return dataService.getGprsDataService().getRoamingProfile();
            }

            throw new NeoDataExtractionException("No roaming profile found in EPS or GPRS data services", null);
        } catch (NeoDataExtractionException e) {
            // Re-throw our custom exceptions
            throw e;
        } catch (Exception e) {
            throw new NeoDataExtractionException("Error extracting roaming profile", null, e);
        }
    }

    /**
     * Extract roaming profile from nested data services
     */
    private String extractGprsRestriction(DataServiceDto dataService) {
        try {
            if (dataService.getEpsDataService() != null &&
                dataService.getEpsDataService().getGprsRestriction() != null) {
                return dataService.getEpsDataService().getGprsRestriction();
            }
            throw new NeoDataExtractionException("No GPRS restriction found in EPS data service", null);
        } catch (NeoDataExtractionException e) {
            // Re-throw our custom exceptions
            throw e;
        } catch (Exception e) {
            throw new NeoDataExtractionException("Error extracting GPRS restriction", null, e);
        }
    }

    /**
     * Calculate IP Messaging Indicator based on SMS and Voice service availability.
     * Returns "TRUE" if both SMS and Voice services are available, otherwise "FALSE".
     *
     * SMS is available if: smsService.getMtMsisdns() != null OR smsService.getMoMsisdns() != null
     * Voice is available if: voiceService.getMsisdns() != null (VoiceServiceDto only has msisdns list)
     *
     * @param subscriberSvc The subscriber services DTO
     * @return "TRUE" if both SMS and Voice services are available, otherwise "FALSE"
     */
    private String calculateIpMessagingIndicator(SubscriberServicesDto subscriberSvc) {
        try {
            boolean smsAvailable = false;
            boolean voiceAvailable = false;

            // Check SMS service availability - SMS service has separate MT and MO MSISDN lists
            if (subscriberSvc != null && subscriberSvc.getSmsService() != null) {
                SmsServiceDto smsService = subscriberSvc.getSmsService();
                smsAvailable = (smsService.getMtMsisdns() != null && !smsService.getMtMsisdns().isEmpty()) ||
                              (smsService.getMoMsisdns() != null && !smsService.getMoMsisdns().isEmpty());
                log.debug("Dynamic mapping: SMS service available: {}", smsAvailable);
            }

            // Check Voice service availability - Voice service has a single msisdns list
            if (subscriberSvc != null && subscriberSvc.getVoiceService() != null) {
                VoiceServiceDto voiceService = subscriberSvc.getVoiceService();
                voiceAvailable = (voiceService.getMsisdns() != null && !voiceService.getMsisdns().isEmpty());
                log.debug("Dynamic mapping: Voice service available: {}", voiceAvailable);
            }

            boolean ipMessagingEnabled = smsAvailable && voiceAvailable;
            String result = ipMessagingEnabled ? NeoConstants.BOOLEAN_TRUE : NeoConstants.BOOLEAN_FALSE;
            log.debug("Dynamic mapping: IP Messaging Indicator - SMS: {}, Voice: {}, Result: {}",
                     smsAvailable, voiceAvailable, result);

            return result;
        } catch (Exception e) {
            throw new NeoDataExtractionException("Error calculating IP Messaging Indicator", null, e);
        }
    }

    /**
     * Calculate Session Types by extracting PdnTypeDto enum values from PDN contexts.
     * This method is used for both allowed and default session types since they have identical logic.
     *
     * @param subscriberSvc The subscriber services DTO
     * @return Comma-separated string of  PdnTypeDto values, or null if none found
     */
    private String calculateSessionTypes(SubscriberServicesDto subscriberSvc) {
        try {
            // CRITICAL VALIDATION: PDN contexts are required for session types calculation
            if (subscriberSvc == null || subscriberSvc.getDataService() == null) {
                throw new NeoDataExtractionException("SubscriberServicesDto or DataService is null - cannot calculate session types", null);
            }

            List<PdnContextDto> pdnContexts = subscriberSvc.getDataService().getPdnContexts();
            if (pdnContexts == null || pdnContexts.isEmpty()) {
                throw new NeoDataExtractionException("PDN contexts are null or empty - cannot calculate session types", null);
            }

            List<String> sessionTypes = new ArrayList<>();

            for (PdnContextDto context : pdnContexts) {
                if (context != null && context.getType() != null) {
                    // Convert PdnTypeDto enum to its string representation using the pdnType field
                    String sessionType = context.getType().pdnType;
                    sessionTypes.add(sessionType);
                    log.debug("Dynamic mapping: Found PDN type: {}", sessionType);
                }
            }

            if (!sessionTypes.isEmpty()) {
                String result = String.join(",", sessionTypes);
                log.debug("Dynamic mapping: Calculated session types: {}", result);
                return result;
            } else {
                throw new NeoDataExtractionException("No valid PDN context types found - cannot calculate session types", null);
            }
        } catch (NeoDataExtractionException e) {
            // Re-throw our custom exceptions
            throw e;
        } catch (Exception e) {
            throw new NeoDataExtractionException("Error calculating session types", null, e);
        }
    }

}

/*
 * ===============================================================================
 * TODO PARAMETER MAPPING & EXCEPTION HANDLING DOCUMENTATION
 * ===============================================================================
 * This section documents ALL 85 parameters mapped by this class with their sources,
 * conditions, and corresponding exception handling strategies.
 * Use this as a reference for understanding both parameter logic and error scenarios.
 *
 * PHASE 1: BASIC IDENTITY (3 parameters)
 * ========================================================
 * 1.  IMSI                           - Direct from method parameter (always required)
 *     Exception: NeoServiceValidationException if null/empty → createProvideRequest()
 * 2.  SUBSCRIPTION_ID                - From subscriberSvc.getId() (nullable)
 *     Exception: NeoServiceValidationException if subscriberSvc null → createProvideRequest()
 * 3.  MSISDN                         - From subscriberSvc.getMsisdn() (nullable)
 *     Exception: NeoServiceValidationException if subscriberSvc null → createProvideRequest()
 *
 * PHASE 2: SERVICE CHARACTERISTICS (11 parameters)
 * =========================================================
 * SMS Service (4 parameters):
 * 4.  SMS                            - From neoClientProperties.getDefaults().getSms() IF (hasMtMsisdns OR hasMoMsisdns)
 *     Exception: NeoServiceValidationException if smsService null → extractServiceCharacteristics()
 * 5.  IS_BAR_OUTGOING_SMS            - From NeoConstants.toNeoBoolean(smsService.getMoBarred()) → "TRUE"/"FALSE"/null
 *     Exception: NeoServiceValidationException if smsService null → extractServiceCharacteristics()
 *     Note: Uses utility function for boolean conversion; null values are skipped by ParameterBuilder
 * 6.  IS_BAR_INCOMING_SMS            - From NeoConstants.toNeoBoolean(smsService.getMtBarred()) → "TRUE"/"FALSE"/null
 *     Exception: NeoServiceValidationException if smsService null → extractServiceCharacteristics()
 *     Note: Uses utility function for boolean conversion; null values are skipped by ParameterBuilder
 * 7.  IS_BAR_OUTGOING_INTERNATIONAL_SMS - From NeoConstants.toNeoBoolean(smsService.getSmsBoiexh()) → "TRUE"/"FALSE"/null
 *     Exception: NeoServiceValidationException if smsService null → extractServiceCharacteristics()
 *     Note: Uses utility function for boolean conversion; null values are skipped by ParameterBuilder
 *
 * Voice Service (2 parameters):
 * 8.  IS_BAR_INCOMING_CALLS          - From NeoConstants.toNeoBoolean(voiceService.getMtBarred()) → "TRUE"/"FALSE"/null
 *     Exception: NeoServiceValidationException if voiceService null → extractServiceCharacteristics()
 *     Note: Uses utility function for boolean conversion; null values are skipped by ParameterBuilder
 * 9.  IS_BAR_OUTGOING_CALLS          - From NeoConstants.toNeoBoolean(voiceService.getMoBarred()) → "TRUE"/"FALSE"/null
 *     Exception: NeoServiceValidationException if voiceService null → extractServiceCharacteristics()
 *     Note: Uses utility function for boolean conversion; null values are skipped by ParameterBuilder
 *
 * Data Service (5 parameters):
 * 10. ROAMING_RESTRICTION            - From extractRoamingProfile() → EPS then GPRS data service
 *     Exception: NeoDataExtractionException if no roaming profile found → extractRoamingProfile()
 * 11. GPRS_ROAMING_RESTRICTION       - From extractGprsRestriction() → EPS data service only
 *     Exception: NeoDataExtractionException if no GPRS restriction found → extractGprsRestriction()
 * 12. PDP_NAME                       - From EpsAndPdnContextExtractor.extractPdpName() → EPS→PDN priority, comma-separated
 *     Exception: NeoServiceValidationException if dataService null → extractServiceCharacteristics()
 * 13. DEFAULT_PDP_NAME               - From calculateDefaultPdpName() → lookup defaultApn in EPS PDN contexts, extract pdpName
 *     Exception: NeoDataExtractionException if APN null/empty, no matching context, or null pdpName → calculateDefaultPdpName()
 *
 * PHASE 3: DYNAMIC CALCULATIONS (7 parameters)
 * ===============================================
 * 14. NETWORK_ACCESS_MODE            - From calculateNetworkAccessModeFromServices() → "00"(full/data), "01"(voice), "09"(default)
 *     Exception: NeoDataExtractionException if unknown service combination → calculateNetworkAccessModeFromServices()
 * 15. ACWSVCNETWORK                  - From calculateAcwsvcnetwork() → IMSI prefix:"5gsaphone", 5G+Voice:"5gphone", 5G only:"5gbasic", else:NULL(omitted)
 *     Exception: NeoDataExtractionException on calculation errors → calculateAcwsvcnetwork()
 *     Note: NULL return is valid business logic (parameter omitted from SOAP XML)
 * 16. HSS_POOL_ID                    - IMSI prefix dependent → "HSA" if hasConfiguredImsiPrefix, else "HSS"
 *     Graceful: hasConfiguredImsiPrefix() returns false on errors (with null safety)
 * 17. BASIC_MSISDN_BC_NAME           - IMSI prefix dependent → "NULL" if hasConfiguredImsiPrefix, else "SPEECH & GPRS"
 *     Graceful: hasConfiguredImsiPrefix() returns false on errors (with null safety)
 * 18. CORE_IND                       - IMSI prefix dependent → "5G" if hasConfiguredImsiPrefix, else "4G"
 *     Graceful: hasConfiguredImsiPrefix() returns false on errors (with null safety)
 * 19. IP_MESSAGING_INDICATOR         - From calculateIpMessagingIndicator() → "TRUE" if (SMS available AND Voice available), else "FALSE"
 *     Exception: NeoDataExtractionException on calculation errors → calculateIpMessagingIndicator()
 *     Graceful: has5gNsaData(), hasVoiceService() return false on errors (service availability checks)
 * 20. EFFECTIVE_DATE                 - From NeoDateUtils.getNeoEffectiveDate() → current UTC in YYYYMMDDHHMISS format
 *     Exception: Any errors in date generation would propagate as runtime exceptions
 *
 * PHASE 3: IMSI PREFIX DEPENDENT (18 parameters - conditional based on IMSI prefix)
 * ============================================================================================
 * 21. SESSION_AMBR_DOWNLINK          - From EpsAndPdnContextExtractor.extractSessionAmbrDownLink → EPS→PDN priority, comma-separated sessionAmbrDownLink (only if hasConfiguredImsiPrefix AND dataService exists)
 *     Graceful: EpsAndPdnContextExtractor logs errors and returns null → parameter skipped
 * 22. SESSION_AMBR_UPLINK            - From EpsAndPdnContextExtractor.extractSessionAmbrUpLink → EPS→PDN priority, comma-separated sessionAmbrUpLink (only if hasConfiguredImsiPrefix AND dataService exists)
 *     Graceful: EpsAndPdnContextExtractor logs errors and returns null → parameter skipped
 * 23. DOWNLOAD_SPEED                 - Constant "10 Gbps" (only if hasConfiguredImsiPrefix)
 *     Graceful: If prefix check fails → parameter skipped (log and continue)
 * 24. UPLOAD_SPEED                   - Constant "10 Gbps" (only if hasConfiguredImsiPrefix)
 *     Graceful: If prefix check fails → parameter skipped (log and continue)
 * 25. DEFAULT_SSC_MODE               - Constant "SSC-MODE-1" (only if hasConfiguredImsiPrefix)
 *     Graceful: If prefix check fails → parameter skipped (log and continue)
 * 26. SLICE_SERVICE_TYPE             - Constant "1" (only if hasConfiguredImsiPrefix)
 *     Graceful: If prefix check fails → parameter skipped (log and continue)
 * 27. SLICE_DIFFERENTIATOR           - Constant "NULL" (only if hasConfiguredImsiPrefix)
 *     Graceful: If prefix check fails → parameter skipped (log and continue)
 * 28. CHARGING_CHARACTERISTIC        - From imsiDependentDefaults.getChargingCharacteristic() (only if hasConfiguredImsiPrefix)
 *     Graceful: If prefix check fails → parameter skipped (log and continue)
 * 29. ARP_PREEMPT_CAP                - From imsiDependentDefaults.getArpPreemptCap() (only if hasConfiguredImsiPrefix)
 *     Graceful: If prefix check fails → parameter skipped (log and continue)
 * 30. ARP_PREEMPT_VULN               - From imsiDependentDefaults.getArpPreemptVuln() (only if hasConfiguredImsiPrefix)
 *     Graceful: If prefix check fails → parameter skipped (log and continue)
 * 31. IWK_EPS_IND                    - From imsiDependentDefaults.getIwkEpsInd() (only if hasConfiguredImsiPrefix)
 *     Graceful: If prefix check fails → parameter skipped (log and continue)
 * 32. DEFAULT_DNN_NAME               - From subscriberSvc.getDefaultApnName() (only if hasConfiguredImsiPrefix)
 *     Graceful: If prefix check fails → parameter skipped (log and continue)
 * 33. ALLOWED_SESSION_TYPES          - From calculateSessionTypes() → comma-separated PDN context types (only if hasConfiguredImsiPrefix)
 *     Exception: NeoDataExtractionException if PDN contexts null/empty → calculateSessionTypes()
 *     Graceful: If prefix check fails → parameter skipped (log and continue)
 * 34. DEFAULT_SESSION_TYPES          - Same as ALLOWED_SESSION_TYPES (only if hasConfiguredImsiPrefix)
 *     Exception: NeoDataExtractionException if PDN contexts null/empty → calculateSessionTypes()
 *     Graceful: If prefix check fails → parameter skipped (log and continue)
 * 35. DNN_NAME                       - From EpsAndPdnContextExtractor.extractDnnName() → EPS→PDN priority, comma-separated apn (only if hasConfiguredImsiPrefix AND dataService exists)
 *     Graceful: EpsAndPdnContextExtractor logs errors and returns null → parameter skipped
 * 36. QOS_PROFILE_5G                 - From EpsAndPdnContextExtractor.extract5gQosProfile() → EPS→PDN priority, comma-separated five_qi_qci (only if hasConfiguredImsiPrefix AND dataService exists)
 *     Graceful: EpsAndPdnContextExtractor logs errors and returns null → parameter skipped
 * 37. ARP_PRIORITY_LEVEL             - From EpsAndPdnContextExtractor.extractArpPriorityLevel() → EPS→PDN priority, comma-separated earp (only if hasConfiguredImsiPrefix AND dataService exists)
 *     Graceful: EpsAndPdnContextExtractor logs errors and returns null → parameter skipped
 * 38. PRIORITY_LEVEL                 - From EpsAndPdnContextExtractor.extractPriorityLevel() → EPS→PDN priority, comma-separated earp (only if hasConfiguredImsiPrefix AND dataService exists)
 *     Graceful: EpsAndPdnContextExtractor logs errors and returns null → parameter skipped
 *
 * PHASE 3: NON-IMSI PREFIX DEPENDENT (1 parameter - only if IMSI does NOT start with configured prefix)
 * ===================================================================================================
 * 39. OTA_VALIDITY                   - From imsiDependentDefaults.getOtaValidity() (only if NOT hasConfiguredImsiPrefix)
 *     Note: This parameter is for NeoCore use case 04 and is only added when IMSI prefix check fails
 *     Graceful: If configuration null → parameter skipped (log and continue)
 *
 * PHASE 4: CONFIGURATION PROPERTIES (13 parameters - from NeoClientProperties.getDefaults())
 * ============================================================================================
 * Exception: NeoConfigurationException if neoClientProperties null → mapConfigurationParameters()
 * Exception: NeoConfigurationException if neoClientProperties.getDefaults() null → mapConfigurationParameters()
 *
 * 40. MARKET                         - From defaults.getMarket() (nullable)
 * 41. SUBMARKET                      - From defaults.getSubMarket() (nullable)
 * 42. HOME_SMSC                      - From defaults.getMarket() (same as MARKET) (nullable)
 * 43. OPERATOR_ID                    - From defaults.getOperatorId() (nullable)
 * 44. ORIGINATING_CLASS_OF_SERVICE   - From defaults.getOriginatingClassOfService() (nullable)
 * 45. CALLER_ID                      - From defaults.getCallerId() (nullable)
 * 46. EMLPP_ACTIVE                   - From defaults.getEmlppActive() (nullable)
 * 47. SUBSCRIPTION_CLASS             - From defaults.getSubscriptionClass() (nullable)
 * 48. CLIENT_APPLICATION_ID          - From defaults.getClientApplicationId() (nullable)
 * 49. CARRIER_ID                     - From defaults.getCarrierId() (nullable)
 * 50. EQUIPMENT_TYPE                 - From defaults.getEquipmentType() (nullable)
 * 51. EXTERNAL_OFFER_ID              - From defaults.getExternalOfferId() (nullable)
 * 52. OFFER_LEVEL                    - From defaults.getOfferLevel() (nullable)
 * 53. ACWACCTSUBTYPE                 - From defaults.getAcwacctsubtype() (nullable)
 * Note: Individual property nulls are handled gracefully (parameter skipped if null)
 * Note: VIDEO_STREAMING_QUALITY, CHARGING_CHARACTERISTIC, ARP_PREEMPT_CAP, ARP_PREEMPT_VULN, IWK_EPS_IND moved to IMSI-prefix dependent logic
 *
 * PHASE 5: HARDCODED DEFAULTS (32 parameters - always added with constant values)
 * =======================================================================================
 * Exception: These parameters use hardcoded constants and should never fail
 * Note: If constant values are missing from NeoConstants, would result in compilation errors
 *
 * 54. MMS                            - Constant "TRUE" (MMS_DEFAULT)
 * 55. PAYMENT_TYPE                   - Constant "PO" (PAYMENT_TYPE_DEFAULT)
 * 56. IS_BAR_WAP                     - Constant "FALSE" (IS_BAR_WAP_DEFAULT)
 * 57. CONTENT_FILTER                 - Constant "FALSE" (CONTENT_FILTER_DEFAULT)
 * 58. IS_AGE_VERIFIED                - Constant "FALSE" (IS_AGE_VERIFIED_DEFAULT)
 * 59. NR_AS_SECONDARY_RAT_ALLOWED    - Constant "TRUE" (NR_AS_SECONDARY_RAT_ALLOWED_DEFAULT)
 * 60. CLIP_ACT                       - Constant "TRUE" (CLIP_ACT_DEFAULT)
 * 61. CLIP_AUTH                      - Constant "TRUE" (CLIP_AUTH_DEFAULT)
 * 62. SUBS_USSD_ENABLED              - Constant "1" (SUBS_USSD_ENABLED_DEFAULT)
 * 63. MPTY_AUTH                      - Constant "FALSE" (MPTY_AUTH_DEFAULT)
 * 64. MPTY_ACT                       - Constant "FALSE" (MPTY_ACT_DEFAULT)
 * 65. HOLD_AUTH                      - Constant "0" (HOLD_AUTH_DEFAULT)
 * 66. HOLD_ACT                       - Constant "0" (HOLD_ACT_DEFAULT)
 * 67. PASSWORD_OPTION_FOR_SDB        - Constant "TRUE" (PASSWORD_OPTION_FOR_SDB_DEFAULT)
 * 68. CFB_AUTH                       - Constant "FALSE" (CFB_AUTH_DEFAULT)
 * 69. CFNRC_AUTH                     - Constant "FALSE" (CFNRC_AUTH_DEFAULT)
 * 70. CFNRY_AUTH                     - Constant "FALSE" (CFNRY_AUTH_DEFAULT)
 * 71. CFD_AUTH                       - Constant "FALSE" (CFD_AUTH_DEFAULT)
 * 72. TELE_CFD_ACT                   - Constant "FALSE" (TELE_CFD_ACT_DEFAULT)
 * 73. TELE_CFD_FTN_ADDR              - Constant "1/1/1/0" (TELE_CFD_FTN_ADDR_DEFAULT)
 * 74. TELE_CFD_FTN_SUB_ADDR          - Constant "1/0/" (TELE_CFD_FTN_SUB_ADDR_DEFAULT)
 * 75. TELE_CFB_NOTIF_CALL_PARTY      - Constant "FALSE" (TELE_CFB_NOTIF_CALL_PARTY_DEFAULT)
 * 76. TELE_CFNRC_NOTIF_CALL_PARTY    - Constant "FALSE" (TELE_CFNRC_NOTIF_CALL_PARTY_DEFAULT)
 * 77. TELE_CFNRY_NOTIF_CALL_PARTY    - Constant "FALSE" (TELE_CFNRY_NOTIF_CALL_PARTY_DEFAULT)
 * 78. CLIR_ACT                       - Constant "TRUE" (CLIR_ACT_DEFAULT)
 * 79. CLIR_AUTH                      - Constant "TRUE" (CLIR_AUTH_DEFAULT)
 * 80. IS_INTERNATIONAL               - Constant "FALSE" (IS_INTERNATIONAL_DEFAULT)
 * 81. GSM_SUBSCRIPT_RESTRICT         - Constant "FALSE" (GSM_SUBSCRIPT_RESTRICT_DEFAULT)
 * 82. CW_AUTH                        - Constant "FALSE" (CW_AUTH_DEFAULT)
 * 83. CW_ACT                         - Constant "FALSE" (CW_ACT_DEFAULT)
 * 84. TELE_CFU_ACT                   - Constant "FALSE" (TELE_CFU_ACT_DEFAULT)
 * 85. CFU_AUTH                       - Constant "FALSE" (CFU_AUTH_DEFAULT)
 * Note: DEFAULT_SSC_MODE, SLICE_SERVICE_TYPE, SLICE_DIFFERENTIATOR moved to IMSI-prefix dependent logic
 *
 * ===============================================================================
 * PARAMETER MAPPING & EXCEPTION HANDLING STRATEGY
 * ===============================================================================
 *
 * TOTAL PARAMETERS: 85
 * - Basic Identity: 3 parameters (always added) → Critical validation with NeoServiceValidationException
 * - Service Characteristics: 11 parameters (conditional) → Critical validation + data extraction exceptions
 * - Dynamic Calculations: 7 parameters (always added) → Mix of critical exceptions and graceful degradation
 * - IMSI Prefix Dependent: 18 parameters (conditional) → Graceful degradation (log and skip)
 * - Non-IMSI Prefix Dependent: 1 parameter (conditional) → Graceful degradation (log and skip)
 * - Configuration Properties: 13 parameters (nullable) → Critical validation for config object, graceful for individual nulls
 * - Hardcoded Defaults: 32 parameters (constants) → No exceptions (compilation-time safety)
 *
 * EXCEPTION STRATEGY :
 * CRITICAL (Throw Exceptions - Stop Processing):
 *    - Entry point validation (IMSI, SubscriberServicesDto null)
 *    - Service validation (Voice, SMS, Data services null)
 *    - PDN context validation (null/empty contexts for calculations)
 *    - Calculation failures (network access mode, session types, etc.)
 *    - Configuration failures (NeoClientProperties null/missing)
 *    - Data extraction failures (roaming profile, GPRS restriction)
 *
 * GRACEFUL DEGRADATION (Log and Continue):
 *    - IMSI prefix dependent parameters (optional enhancements)
 *    - Service availability checks (has5gNsaData, hasVoiceService)
 *    - IMSI prefix validation (hasConfiguredImsiPrefix with null safety)
 *    - Individual configuration property nulls
 *    - ACWSVCNETWORK NULL return (valid business logic for parameter omission)
 *
 * CONDITIONAL LOGIC & EXCEPTION INTEGRATION:
 * - SMS parameter: Added only if MT or MO MSISDNs exist → Exception if smsService null
 * - ACWSVCNETWORK: Omitted if "NULL" (valid business logic) → Exception on calculation errors
 * - IMSI Prefix Dependent: Added only if prefix matches → Graceful degradation if prefix check fails
 * - Service Characteristics: Added only if services exist → Exception if services null
 * - Configuration Properties: Added only if not null → Exception if config object null, graceful for individual nulls
 *
 * DATA EXTRACTION PATTERNS:
 * - EPS PDN Contexts checked first, then PDN Contexts as fallback
 * - Comma-separated values when multiple contexts contain data
 * - Critical calculations throw exceptions if no valid data found
 * - Optional extractions use graceful degradation
 *
 * VALUE FORMAT SPECIFICATIONS:
 * - SMS barring,Voice barring: "TRUE"/"FALSE" (via NeoConstants.toNeoBoolean() utility function)
 * - IP Messaging: "TRUE"/"FALSE"
 * - Network Access Mode: "00"(full/data), "01"(voice)
 * - ACWSVCNETWORK: "5gsaphone"(prefix match), "5gphone"(5G+Voice), "5gbasic"(5G only), NULL(omitted)
 * - Date Format: YYYYMMDDHHMISS (UTC) from NeoDateUtils
 * - Boolean Constants: All boolean values use uppercase "TRUE"/"FALSE" format consistently
 *
 * REMOVED PARAMETERS (No longer in current implementation):
 * - PRIMARY_PUID (commented out in basic identity mapping)
 * - IS_FAX, FAX_NUMBER (removed as per new implementation)
 * - CALLING_LINE_ID_RESTRICTION, ORIG_LINE_ID_RESTRICTION_LEVEL (removed as per new implementation)
 * - CONNECTED_LINE_PRESENTATION, CALLING_NAME_PRESENTATION (removed as per new implementation)
 * - VIDEO_STREAMING_QUALITY (commented out in configuration properties)
 * - INTERZONAL_INTL_OG_NOT_HPLMN, INTL_OG_CALLS_NOT_HPLMN (commented out in hardcoded defaults)
 *
 * MOVED PARAMETERS (Relocated to IMSI-prefix dependent logic):
 * - DEFAULT_SSC_MODE (moved from hardcoded defaults to IMSI-prefix dependent)
 * - SLICE_SERVICE_TYPE (moved from hardcoded defaults to IMSI-prefix dependent)
 * - SLICE_DIFFERENTIATOR (moved from hardcoded defaults to IMSI-prefix dependent)
 * - CHARGING_CHARACTERISTIC (moved from configuration to IMSI-prefix dependent)
 * - ARP_PREEMPT_CAP (moved from configuration to IMSI-prefix dependent)
 * - ARP_PREEMPT_VULN (moved from configuration to IMSI-prefix dependent)
 * - IWK_EPS_IND (moved from configuration to IMSI-prefix dependent)
 * - DEFAULT_DNN_NAME (moved to IMSI-prefix dependent)
 *
 * NEW PARAMETERS ADDED:
 * - OTA_VALIDITY (added for NeoCore use case 04, non-IMSI prefix dependent)
 * - SHORT_MESSAGE_TERMINATION_AUTHORIZATION (re-added to constants)
 *
 * EXCEPTION HIERARCHY:
 * - NeoProvideRequestException (base)
 *   ├── NeoServiceValidationException (input validation failures)
 *   ├── NeoDataExtractionException (calculation/extraction failures)
 *   └── NeoConfigurationException (configuration issues)
 *
 * OPERATIONAL GUIDANCE:
 * - NeoServiceValidationException → Check input data quality and completeness
 * - NeoDataExtractionException → Investigate data consistency and business logic issues
 * - NeoConfigurationException → Verify application configuration and properties
 * - Monitor exception patterns for system health insights and proactive issue resolution
 *
 */

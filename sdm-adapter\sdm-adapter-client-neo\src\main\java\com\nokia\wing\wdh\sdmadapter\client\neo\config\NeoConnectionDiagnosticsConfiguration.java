package com.nokia.wing.wdh.sdmadapter.client.neo.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * Configuration for NEO Connection Diagnostics
 * 
 * Enables additional debugging and monitoring for connection reset issues.
 * This configuration helps diagnose SOAP connection problems.
 */
@Slf4j
@Configuration
@ConditionalOnProperty(value="application.clientType", havingValue="NEO")
public class NeoConnectionDiagnosticsConfiguration {

    @Value("${neo.diagnostics.enableNetworkDebugging:false}")
    private boolean enableNetworkDebugging;
    
    @Value("${neo.diagnostics.enableSslDebugging:false}")
    private boolean enableSslDebugging;
    
    @Value("${neo.diagnostics.enableHttpClientDebugging:false}")
    private boolean enableHttpClientDebugging;
    
    @Value("${neo.diagnostics.logConnectionPoolStats:false}")
    private boolean logConnectionPoolStats;

    @PostConstruct
    public void configureDiagnostics() {
        log.info("=== NEO CONNECTION DIAGNOSTICS CONFIGURATION ===");
        
        if (enableNetworkDebugging) {
            enableNetworkDebugging();
        }
        
        if (enableSslDebugging) {
            enableSslDebugging();
        }
        
        if (enableHttpClientDebugging) {
            enableHttpClientDebugging();
        }
        
        if (logConnectionPoolStats) {
            log.info("Connection pool statistics logging enabled");
        }
        
        // Always log basic connection information
        logSystemNetworkConfiguration();
    }
    
    /**
     * Enable network-level debugging
     */
    private void enableNetworkDebugging() {
        log.warn("ENABLING NETWORK DEBUGGING - This will generate verbose logs!");
        
        // Enable Java networking debug
        System.setProperty("java.net.debug", "all");
        
        // Enable socket debugging
        System.setProperty("javax.net.debug", "ssl,handshake,data,trustmanager");
        
        log.info("Network debugging enabled");
    }
    
    /**
     * Enable SSL debugging
     */
    private void enableSslDebugging() {
        log.warn("ENABLING SSL DEBUGGING - This will generate verbose logs!");
        
        // Enable SSL debugging
        System.setProperty("javax.net.debug", "ssl,handshake");
        
        log.info("SSL debugging enabled");
    }
    
    /**
     * Enable HTTP client debugging
     */
    private void enableHttpClientDebugging() {
        log.warn("ENABLING HTTP CLIENT DEBUGGING - This will generate verbose logs!");
        
        // Enable Apache HTTP Client debugging
        System.setProperty("org.apache.commons.logging.Log", "org.apache.commons.logging.impl.SimpleLog");
        System.setProperty("org.apache.commons.logging.simplelog.showdatetime", "true");
        System.setProperty("org.apache.commons.logging.simplelog.log.httpclient.wire.header", "debug");
        System.setProperty("org.apache.commons.logging.simplelog.log.httpclient.wire.content", "debug");
        System.setProperty("org.apache.commons.logging.simplelog.log.org.apache.http", "debug");
        System.setProperty("org.apache.commons.logging.simplelog.log.org.apache.http.headers", "debug");
        
        log.info("HTTP client debugging enabled");
    }
    
    /**
     * Log system network configuration
     */
    private void logSystemNetworkConfiguration() {
        log.info("=== SYSTEM NETWORK CONFIGURATION ===");
        log.info("Java version: {}", System.getProperty("java.version"));
        log.info("OS: {} {}", System.getProperty("os.name"), System.getProperty("os.version"));
        log.info("Available processors: {}", Runtime.getRuntime().availableProcessors());
        
        // Network-related system properties
        log.info("Network configuration:");
        log.info("  java.net.useSystemProxies: {}", System.getProperty("java.net.useSystemProxies"));
        log.info("  java.net.preferIPv4Stack: {}", System.getProperty("java.net.preferIPv4Stack"));
        log.info("  java.net.preferIPv6Addresses: {}", System.getProperty("java.net.preferIPv6Addresses"));
        log.info("  networkaddress.cache.ttl: {}", System.getProperty("networkaddress.cache.ttl"));
        log.info("  networkaddress.cache.negative.ttl: {}", System.getProperty("networkaddress.cache.negative.ttl"));
        
        // SSL-related system properties
        log.info("SSL configuration:");
        log.info("  https.protocols: {}", System.getProperty("https.protocols"));
        log.info("  https.cipherSuites: {}", System.getProperty("https.cipherSuites"));
        log.info("  javax.net.ssl.trustStore: {}", System.getProperty("javax.net.ssl.trustStore"));
        log.info("  javax.net.ssl.keyStore: {}", System.getProperty("javax.net.ssl.keyStore"));
        
        // Memory information
        Runtime runtime = Runtime.getRuntime();
        log.info("Memory information:");
        log.info("  Max memory: {} MB", runtime.maxMemory() / 1024 / 1024);
        log.info("  Total memory: {} MB", runtime.totalMemory() / 1024 / 1024);
        log.info("  Free memory: {} MB", runtime.freeMemory() / 1024 / 1024);
        log.info("  Used memory: {} MB", (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024);
    }
    
    /**
     * Get connection pool statistics logging flag
     */
    public boolean isLogConnectionPoolStats() {
        return logConnectionPoolStats;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">

    <springProperty name="log.level" source="logging.level.root" defaultValue="INFO" />

    <appender name="JSON_CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder" />
    </appender>

    <root level="${log.level}">
        <appender-ref ref="JSON_CONSOLE" />
    </root>

    <logger name="jakarta.activation" level="WARN"/>
    <logger name="jakarta.mail" level="WARN"/>
    <logger name="jakarta.management.remote" level="WARN"/>
    <logger name="jakarta.xml.bind" level="WARN"/>
<!--    <logger name="ch.qos.logback" level="WARN"/>-->
    <logger name="com.netflix" level="WARN"/>
    <logger name="com.netflix.config.sources.URLConfigurationSource" level="ERROR"/>
    <logger name="com.netflix.discovery" level="INFO"/>
    <logger name="com.ryantenney" level="WARN"/>
    <logger name="com.sun" level="WARN"/>
    <logger name="com.zaxxer" level="WARN"/>
    <logger name="io.undertow" level="WARN"/>
    <logger name="io.undertow.websockets.jsr" level="ERROR"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.apache.catalina.startup.DigesterFactory" level="OFF"/>
    <logger name="org.bson" level="WARN"/>
    <logger name="org.hibernate.validator" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="org.springframework.security" level="WARN"/>
    <logger name="org.springframework.cache" level="WARN"/>
    <logger name="org.thymeleaf" level="WARN"/>
    <logger name="org.xnio" level="WARN"/>
    <logger name="springfox" level="WARN"/>
    <logger name="sun.rmi" level="WARN"/>
    <logger name="springfox.documentation.schema.property" level="ERROR"/>
    <logger name="sun.net.www" level="INFO"/>
    <logger name="sun.rmi.transport" level="WARN"/>
    <!-- See https://github.com/jhipster/generator-jhipster/issues/13835 -->
    <logger name="Validator" level="INFO"/>
    <!-- See https://github.com/jhipster/generator-jhipster/issues/14444 -->
    <logger name="_org.springframework.web.servlet.HandlerMapping.Mappings" level="INFO"/>
    <!-- jhipster-needle-logback-add-log - JHipster will add a new log with level -->

    <!-- https://logback.qos.ch/manual/configuration.html#statusListener and https://jira.qos.ch/browse/LOGBACK-1090 -->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

</configuration>

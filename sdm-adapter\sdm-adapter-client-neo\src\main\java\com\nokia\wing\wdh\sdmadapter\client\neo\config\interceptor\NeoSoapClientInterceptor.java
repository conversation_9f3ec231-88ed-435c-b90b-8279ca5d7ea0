package com.nokia.wing.wdh.sdmadapter.client.neo.config.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ws.client.WebServiceClientException;
import org.springframework.ws.context.MessageContext;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.soap.SoapEnvelope;
import org.springframework.ws.soap.SoapFault;

import javax.xml.transform.TransformerFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.StringWriter;

/**
 * NEO SOAP Client Interceptor
 *
 * Captures and logs SOAP request/response messages for debugging and analysis.
 * This interceptor helps troubleshoot SOAP communication.
 */
public class NeoSoapClientInterceptor implements ClientInterceptor {

    private static final Logger log = LoggerFactory.getLogger(NeoSoapClientInterceptor.class);

    @Override
    public boolean handleRequest(MessageContext messageContext) throws WebServiceClientException {
        log.info("=== NEO SOAP REQUEST ===");

        try {
            if (messageContext.getRequest() instanceof SoapMessage) {
                SoapMessage soapMessage = (SoapMessage) messageContext.getRequest();
                String soapContent = soapMessageToString(soapMessage);

                log.info("Outbound SOAP Request:");
                log.info("\n{}", soapContent);
            }
        } catch (Exception e) {
            log.error("Failed to log SOAP request: {}", e.getMessage(), e);
        }

        return true;
    }

    @Override
    public boolean handleResponse(MessageContext messageContext) throws WebServiceClientException {
        log.info("=== NEO SOAP RESPONSE ===");

        try {
            if (messageContext.getResponse() instanceof SoapMessage) {
                SoapMessage soapMessage = (SoapMessage) messageContext.getResponse();
                String soapContent = soapMessageToString(soapMessage);

                log.info("Inbound SOAP Response:");
                log.info("\n{}", soapContent);
            }
        } catch (Exception e) {
            log.error("Failed to log SOAP response: {}", e.getMessage(), e);
        }

        return true;
    }

    @Override
    public boolean handleFault(MessageContext messageContext) throws WebServiceClientException {
        log.error("=== NEO SOAP FAULT ===");

        try {
            if (messageContext.getResponse() instanceof SoapMessage) {
                SoapMessage soapMessage = (SoapMessage) messageContext.getResponse();
                SoapEnvelope envelope = soapMessage.getEnvelope();
                SoapFault fault = envelope.getBody().getFault();

                if (fault != null) {
                    log.error("SOAP Fault Code: {}", fault.getFaultCode());
                    log.error("SOAP Fault String: {}", fault.getFaultStringOrReason());

                    // Log full SOAP fault message
                    String soapContent = soapMessageToString(soapMessage);
                    log.error("Full SOAP Fault Message:\n{}", soapContent);
                }
            }
        } catch (Exception e) {
            log.error("Failed to log SOAP fault: {}", e.getMessage(), e);
        }

        return true;
    }

    @Override
    public void afterCompletion(MessageContext messageContext, Exception ex) throws WebServiceClientException {
        if (ex != null) {
            log.error("NEO SOAP call completed with exception: {}", ex.getMessage(), ex);

            // Enhanced connection reset error logging
            if (isConnectionResetError(ex)) {
                log.error("=== CONNECTION RESET DETECTED ===");
                log.error("Connection reset error details:");
                log.error("Exception type: {}", ex.getClass().getSimpleName());
                log.error("Root cause: {}", getRootCause(ex).getMessage());
                log.error("Full stack trace:", ex);

                // Log connection state information
                logConnectionDiagnostics();
            }
        } else {
            log.info("NEO SOAP call completed successfully");
        }
    }

    /**
     * Check if the exception is related to connection reset
     */
    private boolean isConnectionResetError(Exception ex) {
        String message = ex.getMessage();
        if (message == null) {
            message = "";
        }

        // Check for various connection reset patterns
        return message.toLowerCase().contains("connection reset") ||
               message.toLowerCase().contains("connection was aborted") ||
               message.toLowerCase().contains("connection closed") ||
               message.toLowerCase().contains("broken pipe") ||
               message.toLowerCase().contains("socket closed") ||
               ex.getClass().getSimpleName().contains("ConnectException") ||
               ex.getClass().getSimpleName().contains("SocketException");
    }

    /**
     * Get the root cause of an exception
     */
    private Throwable getRootCause(Throwable throwable) {
        Throwable cause = throwable;
        while (cause.getCause() != null && cause.getCause() != cause) {
            cause = cause.getCause();
        }
        return cause;
    }

    /**
     * Log connection diagnostics information
     */
    private void logConnectionDiagnostics() {
        try {
            log.error("=== CONNECTION DIAGNOSTICS ===");
            log.error("Current time: {}", java.time.Instant.now());
            log.error("Available processors: {}", Runtime.getRuntime().availableProcessors());
            log.error("Free memory: {} MB", Runtime.getRuntime().freeMemory() / 1024 / 1024);
            log.error("Max memory: {} MB", Runtime.getRuntime().maxMemory() / 1024 / 1024);

            // Log system properties related to networking
            log.error("System networking properties:");
            log.error("java.net.useSystemProxies: {}", System.getProperty("java.net.useSystemProxies"));
            log.error("java.net.preferIPv4Stack: {}", System.getProperty("java.net.preferIPv4Stack"));
            log.error("java.net.preferIPv6Addresses: {}", System.getProperty("java.net.preferIPv6Addresses"));

        } catch (Exception e) {
            log.error("Failed to log connection diagnostics: {}", e.getMessage());
        }
    }

    /**
     * Convert SOAP message to string for logging
     */
    private String soapMessageToString(SoapMessage soapMessage) {
        try {
            StringWriter writer = new StringWriter();
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty("indent", "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

            DOMSource source = new DOMSource(soapMessage.getDocument());
            StreamResult result = new StreamResult(writer);
            transformer.transform(source, result);

            return writer.toString();
        } catch (Exception e) {
            log.error("Failed to convert SOAP message to string: {}", e.getMessage());
            return "Failed to convert SOAP message: " + e.getMessage();
        }
    }
}

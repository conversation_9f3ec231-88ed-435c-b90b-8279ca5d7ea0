<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration>

<configuration scan="true">
    <include resource="org/springframework/boot/logging/logback/base.xml"/>

    <logger name="com.nokia.wdh.sdmadapter.service" level="INFO"/>

    <logger name="tech.jhipster" level="WARN"/>
    <!-- https://www.testcontainers.org/supported_docker_environment/logging_config/ -->
    <logger name="org.testcontainers" level="INFO"/>

    <logger name="jakarta.activation" level="WARN"/>
    <logger name="jakarta.mail" level="WARN"/>
    <logger name="jakarta.xml.bind" level="WARN"/>
    <logger name="ch.qos.logback" level="WARN"/>
    <logger name="com.netflix" level="WARN"/>
    <logger name="com.netflix.config.sources.URLConfigurationSource" level="ERROR"/>
    <logger name="com.netflix.discovery" level="INFO"/>
    <logger name="com.jayway.jsonpath" level="WARN"/>
    <logger name="com.ryantenney" level="WARN"/>
    <logger name="com.sun" level="WARN"/>
    <logger name="com.zaxxer" level="WARN"/>
    <logger name="io.undertow" level="WARN"/>
    <logger name="io.undertow.websockets.jsr" level="ERROR"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.apache.catalina.startup.DigesterFactory" level="OFF"/>
    <logger name="org.bson" level="WARN"/>
    <logger name="org.hibernate.validator" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="org.springframework.security" level="WARN"/>
    <logger name="org.springframework.cache" level="WARN"/>
    <logger name="org.thymeleaf" level="WARN"/>
    <logger name="org.xnio" level="WARN"/>
    <logger name="springfox" level="WARN"/>
    <logger name="sun.rmi" level="WARN"/>
    <logger name="sun.net.www" level="INFO"/>
    <logger name="sun.rmi.transport" level="WARN"/>
    <logger name="com.tngtech.archunit.core.importer" level="ERROR"/>

    <root>
        <level>WARN</level>
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>
